#!/usr/bin/env python3
"""
Emergency fix script to restore server services
"""

import requests
import subprocess
import time

SERVER_IP = "*************"

def check_service_status():
    """Check if services are responding"""
    services = {
        "Admin Frontend": "https://admin.tripxplo.com/",
        "Main Site": "https://tripxplo.com/",
        "API": "https://api.tripxplo.com/v1/api/"
    }
    
    print("🔍 Checking service status...")
    
    for name, url in services.items():
        try:
            response = requests.get(url, timeout=10)
            if response.status_code == 200:
                print(f"✅ {name}: Online")
            else:
                print(f"⚠️ {name}: Status {response.status_code}")
        except requests.exceptions.RequestException as e:
            print(f"❌ {name}: Offline - {e}")

def run_emergency_commands():
    """Run emergency commands to restore services"""
    
    emergency_commands = [
        # Check PM2 status
        "pm2 status",
        
        # Restart critical services
        "pm2 restart admin-frontend",
        "pm2 restart prod-frontend", 
        "pm2 restart tripmilestone-backend",
        "pm2 restart family-api",
        
        # Check nginx status
        "systemctl status nginx --no-pager",
        
        # Restart nginx if needed
        "systemctl restart nginx",
        
        # Check if ports are being used
        "netstat -tlnp | grep :80",
        "netstat -tlnp | grep :443",
        
        # PM2 save
        "pm2 save"
    ]
    
    ssh_base = f"ssh root@{SERVER_IP}"
    
    for cmd in emergency_commands:
        print(f"\n🔧 Running: {cmd}")
        try:
            full_cmd = f'{ssh_base} "{cmd}"'
            result = subprocess.run(full_cmd, shell=True, capture_output=True, text=True, timeout=30)
            
            if result.returncode == 0:
                print(f"✅ Success: {cmd}")
                if result.stdout:
                    print(f"Output: {result.stdout[:500]}")
            else:
                print(f"⚠️ Warning: {cmd} returned {result.returncode}")
                if result.stderr:
                    print(f"Error: {result.stderr[:500]}")
                    
        except subprocess.TimeoutExpired:
            print(f"⏰ Timeout: {cmd}")
        except Exception as e:
            print(f"❌ Error: {cmd} - {e}")

def main():
    print("🚨 Emergency Server Recovery")
    print("=" * 40)
    
    # Check current status
    check_service_status()
    
    print(f"\n🔧 Attempting to restore services...")
    
    # Run emergency commands
    run_emergency_commands()
    
    print(f"\n⏳ Waiting 10 seconds for services to restart...")
    time.sleep(10)
    
    # Check status again
    print(f"\n🔍 Checking services after restart...")
    check_service_status()
    
    print(f"\n📋 Manual steps if issues persist:")
    print(f"1. SSH to server: ssh root@{SERVER_IP}")
    print(f"2. Check PM2: pm2 status")
    print(f"3. Check nginx: systemctl status nginx")
    print(f"4. Restart nginx: systemctl restart nginx")
    print(f"5. Check logs: pm2 logs")

if __name__ == "__main__":
    main()
