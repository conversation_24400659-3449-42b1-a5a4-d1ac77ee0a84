# Backend PDF Extraction - Deployment Guide

## Auto-Deployment Setup

### GitHub Actions Workflow

The backend PDF extraction service is configured for automatic deployment using GitHub Actions on the `hotel-tariff` branch.

**Workflow File**: `.github/workflows/deploy-backend-extraction.yml`

### Deployment Trigger

Auto-deployment is triggered when:
- Code is pushed to the `hotel-tariff` branch
- Pull requests are made to the `hotel-tariff` branch

### Server Configuration

**Target Server**: `*************`
**Deployment Path**: `/root/tariff-extraction`
**Application Name**: `tariff-extraction`

## Manual Deployment

If you need to deploy manually, you can use the deployment script:

```bash
# SSH to the server
ssh root@*************

# Run the deployment script
cd /root/tariff-extraction/Backend
chmod +x deploy.sh
./deploy.sh
```

## Environment Variables

Create a `.env` file in the Backend directory with the following variables:

```env
# Google AI API Key (for Gemini 2.5 Pro)
GOOGLE_API_KEY=your_google_api_key_here

# Mistral AI API Key (for OCR)
MISTRAL_API_KEY=your_mistral_api_key_here

# Flask Configuration
FLASK_ENV=production
FLASK_DEBUG=False
```

## Application Management

### Using PM2 (Recommended)

```bash
# Check application status
pm2 status

# View logs
pm2 logs tariff-extraction

# Restart application
pm2 restart tariff-extraction

# Stop application
pm2 stop tariff-extraction

# Start application
pm2 start tariff-extraction
```

### Manual Management

```bash
# Check if application is running
ps aux | grep app.py

# Kill existing process
pkill -f app.py

# Start application manually
cd /root/tariff-extraction/Backend
python3 app.py
```

## Testing the Deployment

### Health Check

```bash
# Test if the Flask app is responding
curl http://localhost:5000/

# Test the extraction endpoint (with a sample file)
curl -X POST -F "file=@sample.pdf" http://localhost:5000/api/extract-tariff
```

### Log Monitoring

```bash
# PM2 logs (real-time)
pm2 logs tariff-extraction --lines 50

# Manual logs (if not using PM2)
tail -f /tmp/tariff-extraction.log
```

## Troubleshooting

### Common Issues

1. **Dependencies not installed**
   ```bash
   cd /root/tariff-extraction/Backend
   python3 -m pip install -r requirements.txt
   ```

2. **Environment variables missing**
   ```bash
   # Check if .env file exists
   ls -la /root/tariff-extraction/Backend/.env
   
   # Create .env file if missing
   nano /root/tariff-extraction/Backend/.env
   ```

3. **Port already in use**
   ```bash
   # Check what's using port 5000
   lsof -i :5000
   
   # Kill the process
   kill -9 <PID>
   ```

4. **Permission issues**
   ```bash
   # Fix permissions
   chown -R root:root /root/tariff-extraction
   chmod +x /root/tariff-extraction/Backend/deploy.sh
   ```

### GitHub Actions Secrets

Make sure the following secrets are configured in your GitHub repository:

- `LINODE_SSH_PRIVATE_KEY`: Your private SSH key for accessing the Linode server

To add secrets:
1. Go to your GitHub repository
2. Settings → Secrets and variables → Actions
3. Add the required secrets

## Deployment Flow

1. **Code Push**: Developer pushes code to `hotel-tariff` branch
2. **GitHub Actions**: Workflow triggers automatically
3. **Server Connection**: GitHub Actions connects to Linode server via SSH
4. **Code Deployment**: 
   - Removes old deployment
   - Clones latest code from `hotel-tariff` branch
   - Installs dependencies
   - Starts application with PM2
5. **Verification**: Checks if deployment was successful

## API Endpoints

After deployment, the following endpoints are available:

- `GET /`: Health check
- `POST /api/extract-tariff`: PDF tariff extraction

## Monitoring

### Application Status
```bash
# Check if application is running
pm2 status tariff-extraction

# Check system resources
htop
```

### Logs Analysis
```bash
# View recent logs
pm2 logs tariff-extraction --lines 100

# Search for errors
pm2 logs tariff-extraction | grep -i error

# Monitor real-time logs
pm2 logs tariff-extraction --follow
```

## Rollback

If you need to rollback to a previous version:

```bash
# SSH to server
ssh root@*************

# Navigate to deployment directory
cd /root

# Backup current deployment
mv tariff-extraction tariff-extraction-backup

# Clone specific commit or previous version
git clone -b hotel-tariff https://github.com/tripmilestone/Tripmilestone-admin-frontend.git tariff-extraction

# Or checkout specific commit
cd tariff-extraction
git checkout <commit-hash>

# Redeploy
cd Backend
./deploy.sh
```

This setup ensures that every push to the `hotel-tariff` branch automatically deploys the latest backend PDF extraction code to your Linode server.
