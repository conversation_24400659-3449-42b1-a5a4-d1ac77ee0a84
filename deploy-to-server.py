#!/usr/bin/env python3
"""
Manual deployment script that triggers deployment on the server
This runs from your local machine and triggers the webhook on the server
"""

import requests
import time
import json
from datetime import datetime

# Configuration
SERVER_IP = "*************"
WEBHOOK_PORT = 5001
WEBHOOK_URL = f"http://{SERVER_IP}:{WEBHOOK_PORT}"

def print_status(message):
    timestamp = datetime.now().strftime("%H:%M:%S")
    print(f"[{timestamp}] {message}")

def check_server_health():
    """Check if the webhook server is running"""
    try:
        response = requests.get(f"{WEBHOOK_URL}/", timeout=10)
        if response.status_code == 200:
            print_status("✅ Webhook server is running")
            return True
        else:
            print_status(f"⚠️ Webhook server returned status {response.status_code}")
            return False
    except requests.exceptions.RequestException as e:
        print_status(f"❌ Cannot connect to webhook server: {e}")
        return False

def trigger_deployment():
    """Trigger deployment on the server"""
    try:
        print_status("🚀 Triggering deployment...")
        response = requests.post(f"{WEBHOOK_URL}/deploy", timeout=30)
        
        if response.status_code == 202:
            print_status("✅ Deployment started successfully")
            return True
        else:
            print_status(f"❌ Deployment failed with status {response.status_code}")
            print_status(f"Response: {response.text}")
            return False
            
    except requests.exceptions.RequestException as e:
        print_status(f"❌ Failed to trigger deployment: {e}")
        return False

def monitor_deployment():
    """Monitor deployment progress"""
    print_status("📊 Monitoring deployment progress...")
    
    for i in range(30):  # Monitor for up to 5 minutes
        try:
            response = requests.get(f"{WEBHOOK_URL}/status", timeout=10)
            if response.status_code == 200:
                data = response.json()
                pm2_running = data.get('pm2_running', False)
                
                if pm2_running:
                    print_status("✅ Application is running!")
                    return True
                else:
                    print_status(f"⏳ Deployment in progress... ({i+1}/30)")
            else:
                print_status(f"⚠️ Status check failed: {response.status_code}")
                
        except requests.exceptions.RequestException as e:
            print_status(f"⚠️ Status check error: {e}")
        
        time.sleep(10)  # Wait 10 seconds between checks
    
    print_status("⏰ Monitoring timeout - check manually")
    return False

def get_deployment_logs():
    """Get recent deployment logs"""
    try:
        response = requests.get(f"{WEBHOOK_URL}/logs?lines=20", timeout=10)
        if response.status_code == 200:
            data = response.json()
            logs = data.get('logs', [])
            
            print_status("📋 Recent deployment logs:")
            for log in logs:
                print(f"    {log.strip()}")
        else:
            print_status(f"❌ Failed to get logs: {response.status_code}")
            
    except requests.exceptions.RequestException as e:
        print_status(f"❌ Failed to get logs: {e}")

def deploy_via_ssh():
    """Alternative: Deploy directly via SSH"""
    print_status("🔄 Attempting direct SSH deployment...")
    
    import subprocess
    
    ssh_command = [
        "ssh", f"root@{SERVER_IP}",
        "cd /root && curl -sSL https://raw.githubusercontent.com/tripmilestone/Tripmilestone-admin-frontend/hotel-tariff/Backend/auto-deploy-server.sh | bash"
    ]
    
    try:
        print_status("📡 Connecting via SSH...")
        result = subprocess.run(ssh_command, capture_output=True, text=True, timeout=600)
        
        if result.returncode == 0:
            print_status("✅ SSH deployment completed successfully!")
            print_status("📋 Output:")
            print(result.stdout)
            return True
        else:
            print_status(f"❌ SSH deployment failed with return code {result.returncode}")
            print_status(f"Error: {result.stderr}")
            print_status(f"Output: {result.stdout}")
            return False
            
    except subprocess.TimeoutExpired:
        print_status("⏰ SSH deployment timed out")
        return False
    except Exception as e:
        print_status(f"❌ SSH deployment error: {e}")
        return False

def main():
    """Main deployment function"""
    print("🚀 Backend PDF Extraction - Manual Deployment")
    print("=" * 50)
    
    # Method 1: Try webhook deployment
    print_status("Method 1: Webhook deployment")
    
    if check_server_health():
        if trigger_deployment():
            if monitor_deployment():
                get_deployment_logs()
                print_status("🎉 Deployment completed successfully!")
                return
    
    # Method 2: Try SSH deployment
    print_status("\nMethod 2: Direct SSH deployment")
    
    if deploy_via_ssh():
        print_status("🎉 SSH deployment completed successfully!")
        return
    
    # Method 3: Manual instructions
    print_status("\nMethod 3: Manual deployment")
    print_status("Please run these commands manually on your server:")
    print()
    print(f"ssh root@{SERVER_IP}")
    print("cd /root")
    print("curl -sSL https://raw.githubusercontent.com/tripmilestone/Tripmilestone-admin-frontend/hotel-tariff/Backend/auto-deploy-server.sh | bash")
    print()
    print_status("Or download and run the script:")
    print("wget https://raw.githubusercontent.com/tripmilestone/Tripmilestone-admin-frontend/hotel-tariff/Backend/auto-deploy-server.sh")
    print("chmod +x auto-deploy-server.sh")
    print("./auto-deploy-server.sh")

if __name__ == "__main__":
    main()
