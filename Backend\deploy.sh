#!/bin/bash

# Backend PDF Extraction Deployment Script
# This script can be run manually on the server for deployment

echo "🚀 Starting Backend PDF Extraction Deployment..."

# Configuration
REPO_URL="https://github.com/tripmilestone/Tripmilestone-admin-frontend.git"
BRANCH="hotel-tariff"
DEPLOY_DIR="/root/tariff-extraction"
APP_NAME="tariff-extraction"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if running as root
if [ "$EUID" -ne 0 ]; then
    print_error "Please run this script as root"
    exit 1
fi

# Navigate to deployment directory
cd /root || exit 1

# Remove existing deployment
if [ -d "$DEPLOY_DIR" ]; then
    print_status "Removing existing deployment..."
    rm -rf "$DEPLOY_DIR"
    print_success "Existing deployment removed"
fi

# Clone the repository
print_status "Cloning repository (branch: $BRANCH)..."
if git clone -b "$BRANCH" "$REPO_URL" tariff-extraction; then
    print_success "Repository cloned successfully"
else
    print_error "Failed to clone repository"
    exit 1
fi

# Navigate to Backend directory
cd "$DEPLOY_DIR/Backend" || exit 1

# Detect Python command
if command -v python3 &> /dev/null; then
    PYTHON_CMD="python3"
elif command -v python &> /dev/null; then
    PYTHON_CMD="python"
else
    print_error "Python not found!"
    exit 1
fi

print_status "Using Python: $PYTHON_CMD"

# Install dependencies
print_status "Installing Python dependencies..."
if [ -f "requirements.txt" ]; then
    $PYTHON_CMD -m pip install --upgrade pip
    if $PYTHON_CMD -m pip install -r requirements.txt; then
        print_success "Dependencies installed successfully"
    else
        print_error "Failed to install dependencies"
        exit 1
    fi
else
    print_warning "No requirements.txt found"
fi

# Check environment file
if [ -f ".env" ]; then
    print_success "Environment file found"
else
    print_warning "No .env file found. You may need to create one with API keys."
    print_status "Required environment variables:"
    echo "  - GOOGLE_API_KEY=your_google_api_key"
    echo "  - MISTRAL_API_KEY=your_mistral_api_key"
fi

# Manage with PM2 if available
if command -v pm2 &> /dev/null; then
    print_status "Managing application with PM2..."
    
    # Stop and delete existing process
    pm2 stop "$APP_NAME" 2>/dev/null || true
    pm2 delete "$APP_NAME" 2>/dev/null || true
    
    # Start new process
    if pm2 start app.py --name "$APP_NAME" --interpreter "$PYTHON_CMD"; then
        pm2 save
        print_success "Application started with PM2"
        pm2 status
    else
        print_error "Failed to start application with PM2"
        exit 1
    fi
    
else
    print_warning "PM2 not found. Starting application in background..."
    nohup $PYTHON_CMD app.py > /tmp/tariff-extraction.log 2>&1 &
    print_success "Application started in background"
fi

# Final status
print_success "🎉 Deployment completed successfully!"
echo ""
print_status "📍 Deployment Details:"
echo "  - Location: $DEPLOY_DIR"
echo "  - Backend: $DEPLOY_DIR/Backend"
echo "  - Branch: $BRANCH"
echo "  - Python: $PYTHON_CMD"
echo ""
print_status "🔧 Management Commands:"
echo "  - Check status: pm2 status"
echo "  - View logs: pm2 logs $APP_NAME"
echo "  - Restart: pm2 restart $APP_NAME"
echo "  - Stop: pm2 stop $APP_NAME"
echo ""
print_status "🌐 Testing the application..."
sleep 3
if curl -f http://localhost:5000/ 2>/dev/null; then
    print_success "✅ Application is responding!"
else
    print_warning "⚠️ Application may still be starting up"
fi
