from flask import Flask, request, jsonify
from flask_cors import CORS
import os
import sys
import tempfile
from extract_tariff import extract_tariff_from_pdf, calculate_final_price
import traceback
import json
from datetime import datetime
import uuid

app = Flask(__name__)
CORS(app)  # Enable CORS for all routes

# In-memory storage for tariff data (replace with database in production)
tariff_storage = {}

@app.route('/api/extract-tariff', methods=['POST'])
def extract_tariff():
    """
    API endpoint to extract tariff data from uploaded PDF file.
    Expects a PDF file in the request and returns extracted tariff data.
    """
    try:
        print(f"Received request: {request.method} {request.url}")
        print(f"Request files: {list(request.files.keys())}")
        print(f"Request form: {dict(request.form)}")

        # Check if file is present in request
        if 'file' not in request.files:
            print("Error: No file in request")
            return jsonify({
                'success': False,
                'error': 'No file provided'
            }), 400
        
        file = request.files['file']
        print(f"File object: {file}")
        print(f"File filename: {file.filename}")
        print(f"File content type: {file.content_type}")

        # Check if file is selected
        if not file.filename or file.filename == '':
            print("Error: No filename provided")
            return jsonify({
                'success': False,
                'error': 'No file selected'
            }), 400

        # Check if file is PDF
        if not file.filename.lower().endswith('.pdf'):
            print(f"Error: Invalid file type. Filename: {file.filename}")
            return jsonify({
                'success': False,
                'error': 'Only PDF files are supported'
            }), 400
        
        # Note: The new extraction system always uses LLM orchestration
        
        # Save uploaded file to temporary location
        with tempfile.NamedTemporaryFile(delete=False, suffix='.pdf') as temp_file:
            file.save(temp_file.name)
            temp_path = temp_file.name
        
        try:
            print(f"🚀 API: Starting extraction for file: {file.filename}")
            print(f"🚀 API: Temp file path: {temp_path}")
            print(f"🚀 API: File size: {os.path.getsize(temp_path)} bytes")

            # This function now returns a dictionary with structured data
            extracted_data = extract_tariff_from_pdf(pdf_path=temp_path)

            print(f"API: Extracted data type: {type(extracted_data)}")
            print(f"API: Extracted data keys: {list(extracted_data.keys()) if isinstance(extracted_data, dict) else 'Not a dict'}")
            print(f"API: Full extracted data: {extracted_data}")

            # Clean up temporary file
            os.unlink(temp_path)

            # Check if there was an error during extraction
            if isinstance(extracted_data, dict) and "error" in extracted_data:
                return jsonify({
                    'success': False,
                    'error': f'Extraction failed: {extracted_data["error"]}'
                }), 400

            # Check if we got valid structured data
            if not extracted_data or not isinstance(extracted_data, dict):
                return jsonify({
                    'success': False,
                    'error': 'Invalid response from extraction process.'
                }), 400

            # Check if we have any data
            base_tariffs = extracted_data.get("base_tariffs", [])
            pricing_rules = extracted_data.get("pricing_rules", [])

            if not base_tariffs and not pricing_rules:
                return jsonify({
                    'success': False,
                    'error': 'No structured data could be extracted from the PDF. Check server logs for details.'
                }), 400

            print(f"API: Returning {len(base_tariffs)} base tariffs and {len(pricing_rules)} pricing rules")

            return jsonify({
                'success': True,
                'data': extracted_data,  # Return the whole structured object
                'summary': {
                    'base_tariffs_count': len(base_tariffs),
                    'pricing_rules_count': len(pricing_rules),
                    'total_count': len(base_tariffs) + len(pricing_rules)
                }
            })
            
        except Exception as e:
            # Clean up temporary file in case of error
            if os.path.exists(temp_path):
                os.unlink(temp_path)
            raise e
            
    except Exception as e:
        print(f"Error extracting tariff: {str(e)}")
        print(traceback.format_exc())
        return jsonify({
            'success': False,
            'error': f'Error processing PDF: {str(e)}'
        }), 500

@app.route('/api/health', methods=['GET'])
def health_check():
    """Health check endpoint"""
    return jsonify({
        'success': True,
        'message': 'Tariff extraction service is running'
    })

@app.route('/api/debug', methods=['GET'])
def debug_status():
    """Debug endpoint to check system status"""
    try:
        from dotenv import load_dotenv
        load_dotenv()

        # Check environment variables
        mistral_key = os.getenv("MISTRAL_API_KEY")
        google_key = os.getenv("GOOGLE_API_KEY")

        # Test imports
        import_status = {}
        try:
            from mistralai import Mistral
            import_status['mistral'] = 'OK'
        except Exception as e:
            import_status['mistral'] = f'ERROR: {str(e)}'

        try:
            from langchain_google_genai import ChatGoogleGenerativeAI
            import_status['langchain_google'] = 'OK'
        except Exception as e:
            import_status['langchain_google'] = f'ERROR: {str(e)}'

        try:
            from extract_tariff import extract_tariff_from_pdf
            import_status['extract_tariff'] = 'OK'
        except Exception as e:
            import_status['extract_tariff'] = f'ERROR: {str(e)}'

        return jsonify({
            'success': True,
            'environment': {
                'mistral_api_key': 'SET' if mistral_key else 'MISSING',
                'google_api_key': 'SET' if google_key else 'MISSING'
            },
            'imports': import_status,
            'python_version': sys.version,
            'working_directory': os.getcwd()
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/test-extraction', methods=['POST'])
def test_extraction():
    """Test endpoint with minimal extraction logic"""
    try:
        if 'file' not in request.files:
            return jsonify({'success': False, 'error': 'No file provided'}), 400

        file = request.files['file']
        if not file.filename or not file.filename.lower().endswith('.pdf'):
            return jsonify({'success': False, 'error': 'Invalid file'}), 400

        # Save file temporarily
        with tempfile.NamedTemporaryFile(delete=False, suffix='.pdf') as temp_file:
            file.save(temp_file.name)
            temp_path = temp_file.name

        try:
            # Test just OCR first
            from mistralai import Mistral
            from extract_tariff import extract_text_from_pdf

            api_key = os.getenv("MISTRAL_API_KEY")
            if not api_key:
                return jsonify({'success': False, 'error': 'Missing Mistral API key'}), 400

            ocr_client = Mistral(api_key=api_key)
            text = extract_text_from_pdf(temp_path, ocr_client)

            os.unlink(temp_path)  # Clean up

            if text:
                return jsonify({
                    'success': True,
                    'message': 'OCR successful',
                    'text_length': len(text),
                    'text_preview': text[:500] + '...' if len(text) > 500 else text
                })
            else:
                return jsonify({'success': False, 'error': 'OCR failed - no text extracted'}), 400

        except Exception as e:
            if os.path.exists(temp_path):
                os.unlink(temp_path)
            raise e

    except Exception as e:
        return jsonify({
            'success': False,
            'error': f'Test extraction failed: {str(e)}'
        }), 500

@app.route('/api/test-llm', methods=['GET'])
def test_llm():
    """Test endpoint to check if LLM is working"""
    try:
        from dotenv import load_dotenv
        load_dotenv()

        google_api_key = os.getenv("GOOGLE_API_KEY")
        if not google_api_key:
            return jsonify({'success': False, 'error': 'Missing Google API key'}), 400

        from langchain_google_genai import ChatGoogleGenerativeAI
        from langchain_core.pydantic_v1 import SecretStr

        print("🔧 Creating LLM client for test...")
        llm_client = ChatGoogleGenerativeAI(
            model="gemini-pro",
            api_key=SecretStr(google_api_key)
        )
        print("✅ LLM client created")

        print("🔧 Testing simple LLM call...")
        test_prompt = "Hello! Please respond with a simple JSON object: {\"status\": \"working\", \"message\": \"LLM is functioning\"}"

        response = llm_client.invoke(test_prompt)
        response_content = response.content if hasattr(response, 'content') else str(response)

        print(f"✅ LLM responded: {response_content}")

        return jsonify({
            'success': True,
            'message': 'LLM test successful',
            'response': response_content,
            'response_length': len(response_content) if response_content else 0
        })

    except Exception as e:
        print(f"❌ LLM test failed: {e}")
        import traceback
        traceback.print_exc()
        return jsonify({
            'success': False,
            'error': f'LLM test failed: {str(e)}'
        }), 500

@app.route('/api/test-ocr-only', methods=['POST'])
def test_ocr_only():
    """Test endpoint to check OCR functionality only"""
    try:
        if 'file' not in request.files:
            return jsonify({'success': False, 'error': 'No file provided'}), 400

        file = request.files['file']
        if not file.filename or not file.filename.lower().endswith('.pdf'):
            return jsonify({'success': False, 'error': 'Invalid file'}), 400

        # Save file temporarily
        with tempfile.NamedTemporaryFile(delete=False, suffix='.pdf') as temp_file:
            file.save(temp_file.name)
            temp_path = temp_file.name

        try:
            from dotenv import load_dotenv
            load_dotenv()

            api_key = os.getenv("MISTRAL_API_KEY")
            if not api_key:
                return jsonify({'success': False, 'error': 'Missing Mistral API key'}), 400

            from extract_tariff import extract_text_from_pdf

            print(f"🔍 Testing OCR with file: {file.filename}")
            print(f"🔍 Temp file path: {temp_path}")
            print(f"🔍 File size: {os.path.getsize(temp_path)} bytes")

            text = extract_text_from_pdf(temp_path, api_key)

            os.unlink(temp_path)  # Clean up

            if text:
                return jsonify({
                    'success': True,
                    'message': 'OCR successful',
                    'text_length': len(text),
                    'text_preview': text[:1000] + '...' if len(text) > 1000 else text,
                    'filename': file.filename
                })
            else:
                return jsonify({
                    'success': False,
                    'error': 'OCR failed - no text extracted. Check server logs for details.'
                }), 400

        except Exception as e:
            if os.path.exists(temp_path):
                os.unlink(temp_path)
            raise e

    except Exception as e:
        print(f"❌ OCR test failed: {e}")
        import traceback
        traceback.print_exc()
        return jsonify({
            'success': False,
            'error': f'OCR test failed: {str(e)}'
        }), 500

@app.route('/api/calculate-price', methods=['POST'])
def calculate_price():
    """Test endpoint to demonstrate the pricing engine"""
    try:
        data = request.get_json()

        # Validate required fields
        required_fields = ['structured_data', 'check_in_date', 'room_category', 'meal_plan']
        for field in required_fields:
            if field not in data:
                return jsonify({
                    'success': False,
                    'error': f'Missing required field: {field}'
                }), 400

        structured_data = data['structured_data']
        check_in_date = data['check_in_date']
        room_category = data['room_category']
        meal_plan = data['meal_plan']

        print(f"🔍 Pricing request: {room_category}, {meal_plan}, {check_in_date}")

        # Calculate the final price
        final_price = calculate_final_price(
            structured_data=structured_data,
            check_in_date_str=check_in_date,
            room_category=room_category,
            meal_plan=meal_plan
        )

        if final_price is None:
            return jsonify({
                'success': False,
                'error': 'Could not calculate price. Check room category and meal plan.'
            }), 400

        return jsonify({
            'success': True,
            'final_price': final_price,
            'details': {
                'room_category': room_category,
                'meal_plan': meal_plan.upper(),
                'check_in_date': check_in_date,
                'currency': 'INR'
            }
        })

    except Exception as e:
        print(f"❌ Pricing calculation failed: {e}")
        import traceback
        traceback.print_exc()
        return jsonify({
            'success': False,
            'error': f'Pricing calculation failed: {str(e)}'
        }), 500

@app.route('/api/demo-pricing', methods=['GET'])
def demo_pricing():
    """Demo endpoint that uses the extracted Upavan Resort data to show pricing examples"""
    try:
        # Sample structured data from your Upavan Resort extraction
        sample_data = {
            "base_tariffs": [
                {"room_category": "GREENVIEWDELUXENON-AC", "cp_price": 3500, "map_price": 4500, "ap_price": 5500},
                {"room_category": "HONEYMOONNESTNON-AC", "cp_price": 4500, "map_price": 5500, "ap_price": 6500},
                {"room_category": "HONEYMOONNESTAC", "cp_price": 5500, "map_price": 6500, "ap_price": 7500},
                {"room_category": "HILLVIEWPREMIUMAC", "cp_price": 6500, "map_price": 7500, "ap_price": 8500}
            ],
            "pricing_rules": [
                {"rule_name": "Seasonal Hike", "start_date": "2024-12-20", "end_date": "2025-01-15", "charge_type": "per_room", "amount": 25},
                {"rule_name": "New Year Gala Dinner Adult", "start_date": None, "end_date": None, "charge_type": "per_adult", "amount": 1500},
                {"rule_name": "New Year Gala Dinner Child", "start_date": None, "end_date": None, "charge_type": "per_child", "amount": 1200}
            ]
        }

        # Calculate prices for different scenarios
        examples = []

        # Example 1: Regular date (no surcharges)
        regular_price = calculate_final_price(sample_data, "2025-03-15", "GREENVIEW", "cp")
        examples.append({
            "scenario": "Regular Date",
            "date": "2025-03-15",
            "room": "Green View Deluxe Non-AC",
            "meal_plan": "CP",
            "final_price": regular_price,
            "notes": "Base price only, no surcharges apply"
        })

        # Example 2: Peak season date (with surcharge)
        peak_price = calculate_final_price(sample_data, "2025-01-01", "GREENVIEW", "cp")
        examples.append({
            "scenario": "Peak Season",
            "date": "2025-01-01",
            "room": "Green View Deluxe Non-AC",
            "meal_plan": "CP",
            "final_price": peak_price,
            "notes": "Base price + seasonal hike surcharge"
        })

        # Example 3: Premium room with MAP
        premium_price = calculate_final_price(sample_data, "2025-03-15", "HILLVIEW", "map")
        examples.append({
            "scenario": "Premium Room",
            "date": "2025-03-15",
            "room": "Hill View Premium AC",
            "meal_plan": "MAP",
            "final_price": premium_price,
            "notes": "Premium room with Modified American Plan"
        })

        return jsonify({
            'success': True,
            'message': 'Pricing engine demo with Upavan Resort data',
            'examples': examples,
            'available_rooms': [t['room_category'] for t in sample_data['base_tariffs']],
            'available_plans': ['cp', 'map', 'ap'],
            'pricing_rules': sample_data['pricing_rules']
        })

    except Exception as e:
        print(f"❌ Demo pricing failed: {e}")
        return jsonify({
            'success': False,
            'error': f'Demo pricing failed: {str(e)}'
        }), 500

# Tariff Management Endpoints

@app.route('/admin/hotel/<hotel_id>/tariffs', methods=['GET'])
def get_hotel_tariffs(hotel_id):
    """Get all tariff uploads for a specific hotel"""
    try:
        hotel_tariffs = []
        for tariff_id, tariff_data in tariff_storage.items():
            if tariff_data.get('hotelId') == hotel_id:
                hotel_tariffs.append(tariff_data)

        return jsonify({
            'success': True,
            'result': hotel_tariffs
        })
    except Exception as e:
        print(f"Error fetching hotel tariffs: {str(e)}")
        return jsonify({
            'success': False,
            'error': f'Error fetching tariffs: {str(e)}'
        }), 500

@app.route('/admin/hotel/tariff', methods=['POST'])
def create_tariff_upload():
    """Create a new tariff upload"""
    try:
        data = request.get_json()

        # Generate a unique tariff ID
        tariff_id = str(uuid.uuid4())

        # Create tariff object
        tariff = {
            'tariffId': tariff_id,
            'hotelId': data.get('hotelId'),
            'roomId': data.get('roomId'),
            'filePath': data.get('filePath'),
            'uploadDate': datetime.now().isoformat(),
            'status': 'pending'
        }

        # Include priceData if provided
        if 'priceData' in data and data['priceData']:
            tariff['priceData'] = data['priceData']
            print(f"Storing priceData for tariff {tariff_id}: {len(data['priceData'])} records")

        # Store in memory
        tariff_storage[tariff_id] = tariff

        return jsonify({
            'success': True,
            'result': tariff
        })
    except Exception as e:
        print(f"Error creating tariff upload: {str(e)}")
        return jsonify({
            'success': False,
            'error': f'Error creating tariff: {str(e)}'
        }), 500

@app.route('/admin/hotel/tariff/<tariff_id>', methods=['PUT'])
def update_tariff_status(tariff_id):
    """Update tariff status (approve/reject)"""
    try:
        data = request.get_json()

        # Check if tariff exists
        if tariff_id not in tariff_storage:
            return jsonify({
                'success': False,
                'error': 'Tariff not found'
            }), 404

        # Update tariff data
        tariff = tariff_storage[tariff_id]
        tariff['status'] = data.get('status')
        tariff['priceData'] = data.get('priceData')
        tariff['notes'] = data.get('notes')
        tariff['approvalDate'] = datetime.now().isoformat()
        tariff['approvedBy'] = 'Admin User'  # In production, get from auth context

        return jsonify({
            'success': True,
            'result': tariff
        })
    except Exception as e:
        print(f"Error updating tariff status: {str(e)}")
        return jsonify({
            'success': False,
            'error': f'Error updating tariff: {str(e)}'
        }), 500

@app.route('/admin/hotel/tariff/<tariff_id>', methods=['DELETE'])
def delete_tariff_upload(tariff_id):
    """Delete a tariff upload"""
    try:
        # Check if tariff exists
        if tariff_id not in tariff_storage:
            return jsonify({
                'success': False,
                'error': 'Tariff not found'
            }), 404

        # Delete tariff
        del tariff_storage[tariff_id]

        return jsonify({
            'success': True,
            'result': {'success': True}
        })
    except Exception as e:
        print(f"Error deleting tariff: {str(e)}")
        return jsonify({
            'success': False,
            'error': f'Error deleting tariff: {str(e)}'
        }), 500

if __name__ == '__main__':
    # Create output directory if it doesn't exist
    os.makedirs('output', exist_ok=True)
    
    # Run the Flask app
    app.run(debug=True, host='0.0.0.0', port=5000)