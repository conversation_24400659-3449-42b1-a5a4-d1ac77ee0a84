#!/usr/bin/env python3
"""
Enhanced Pricing Engine Demo
Demonstrates how to integrate pricing rules & surcharges with specific room categories, 
separate date ranges, and pricing calculations.
"""

import json
import sys
import os
from datetime import datetime, timedelta

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from extract_tariff import calculate_final_price

def create_sample_tariff_data():
    """Create comprehensive sample tariff data with enhanced pricing rules."""
    return {
        "base_tariffs": [
            {
                "room_category": "Deluxe Room",
                "cp_price": 5000,
                "map_price": 6000,
                "ap_price": 7000,
                "start_date": "2025-01-01",
                "end_date": "2025-12-31"
            },
            {
                "room_category": "Suite Room",
                "cp_price": 8000,
                "map_price": 9000,
                "ap_price": 10000,
                "start_date": "2025-01-01",
                "end_date": "2025-12-31"
            },
            {
                "room_category": "Premium Villa",
                "cp_price": 12000,
                "map_price": 14000,
                "ap_price": 16000,
                "start_date": "2025-01-01",
                "end_date": "2025-12-31"
            }
        ],
        "pricing_rules": [
            {
                "rule_name": "Christmas & New Year Peak Season",
                "start_date": "2025-12-24",
                "end_date": "2026-01-02",
                "charge_type": "percentage",
                "amount": 0.30,  # 30% surcharge
                "applicable_room_categories": [],  # Applies to all rooms
                "applicable_meal_plans": [],  # Applies to all meal plans
                "rule_type": "seasonal_rate"
            },
            {
                "rule_name": "Weekend Surcharge",
                "start_date": "2025-07-26",  # Saturday
                "end_date": "2025-07-27",    # Sunday
                "charge_type": "percentage",
                "amount": 0.15,  # 15% surcharge
                "applicable_room_categories": ["Deluxe Room", "Suite Room"],
                "applicable_meal_plans": [],
                "rule_type": "surcharge"
            },
            {
                "rule_name": "Premium Villa Special Discount",
                "start_date": "2025-08-01",
                "end_date": "2025-08-15",
                "charge_type": "fixed_amount",
                "amount": 2000,  # ₹2000 discount
                "applicable_room_categories": ["Premium Villa"],
                "applicable_meal_plans": ["ap"],  # Only for AP plan
                "rule_type": "discount"
            },
            {
                "rule_name": "Extra Adult Charge",
                "start_date": None,
                "end_date": None,
                "charge_type": "per_adult",
                "amount": 1500,
                "applicable_room_categories": [],
                "applicable_meal_plans": [],
                "rule_type": "extra_person"
            },
            {
                "rule_name": "Extra Child Charge (5-12 years)",
                "start_date": None,
                "end_date": None,
                "charge_type": "per_child",
                "amount": 800,
                "applicable_room_categories": [],
                "applicable_meal_plans": [],
                "rule_type": "extra_person"
            },
            {
                "rule_name": "GST (18%)",
                "start_date": None,
                "end_date": None,
                "charge_type": "percentage",
                "amount": 0.18,  # 18% GST on total (room + extra person charges)
                "applicable_room_categories": [],
                "applicable_meal_plans": [],
                "rule_type": "mandatory_fee"
            },
            {
                "rule_name": "Resort Fee",
                "start_date": None,
                "end_date": None,
                "charge_type": "per_room",
                "amount": 500,
                "applicable_room_categories": ["Premium Villa"],
                "applicable_meal_plans": [],
                "rule_type": "mandatory_fee"
            }
        ]
    }

def test_pricing_scenarios():
    """Test various pricing scenarios to demonstrate the enhanced integration."""
    print("🏨 Enhanced Pricing Engine Demo")
    print("=" * 60)
    
    tariff_data = create_sample_tariff_data()
    
    # Test scenarios
    scenarios = [
        {
            "name": "Regular Weekday - Deluxe Room",
            "date": "2025-07-25",  # Friday
            "room": "Deluxe Room",
            "plan": "map",
            "adults": 2,
            "children": 0
        },
        {
            "name": "Weekend Surcharge - Suite Room",
            "date": "2025-07-26",  # Saturday
            "room": "Suite Room", 
            "plan": "ap",
            "adults": 2,
            "children": 1
        },
        {
            "name": "Peak Season - Christmas",
            "date": "2025-12-25",  # Christmas
            "room": "Premium Villa",
            "plan": "ap",
            "adults": 4,
            "children": 2
        },
        {
            "name": "Special Discount Period - Premium Villa",
            "date": "2025-08-10",
            "room": "Premium Villa",
            "plan": "ap",
            "adults": 2,
            "children": 0
        },
        {
            "name": "Festival Surcharge Logic Demo",
            "date": "2025-12-25",  # Christmas
            "room": "Deluxe Room",
            "plan": "map",
            "adults": 4,  # 2 extra adults to show surcharge applies only to room
            "children": 2
        }
    ]
    
    for i, scenario in enumerate(scenarios, 1):
        print(f"\n📋 Scenario {i}: {scenario['name']}")
        print("-" * 50)
        print(f"Date: {scenario['date']}")
        print(f"Room: {scenario['room']}")
        print(f"Meal Plan: {scenario['plan'].upper()}")
        print(f"Guests: {scenario['adults']} adults, {scenario['children']} children")
        
        result = calculate_final_price(
            tariff_data,
            scenario['date'],
            scenario['room'],
            scenario['plan'],
            scenario['adults'],
            scenario['children']
        )
        
        if result:
            print_pricing_breakdown(result)
        else:
            print("❌ Could not calculate pricing for this scenario")

def print_pricing_breakdown(pricing_result):
    """Print a detailed pricing breakdown."""
    print(f"\n💰 Pricing Breakdown:")
    print(f"   Base Price ({pricing_result['meal_plan'].upper()}): ₹{pricing_result['base_price']:,}")
    
    if pricing_result['applied_rules']:
        print(f"   Applied Rules:")
        for rule in pricing_result['applied_rules']:
            sign = "+" if rule['amount'] >= 0 else ""
            print(f"     • {rule['description']}: {sign}₹{rule['amount']:,}")
    
    print(f"   Surcharges: +₹{pricing_result['surcharges']:,}")
    print(f"   Discounts: -₹{pricing_result['discounts']:,}")
    print(f"   Extra Person Charges: +₹{pricing_result['extra_person_charges']:,}")
    print(f"   Mandatory Fees: +₹{pricing_result['mandatory_fees']:,}")
    print(f"   " + "─" * 40)
    print(f"   🎯 FINAL PRICE: ₹{pricing_result['final_price']:,}")

def demonstrate_rule_integration():
    """Demonstrate how rules integrate with room categories and date ranges."""
    print(f"\n\n🔧 Rule Integration Demonstration")
    print("=" * 60)
    
    tariff_data = create_sample_tariff_data()
    
    print("📊 Available Room Categories:")
    for tariff in tariff_data['base_tariffs']:
        print(f"   • {tariff['room_category']}: CP ₹{tariff['cp_price']:,}, MAP ₹{tariff['map_price']:,}, AP ₹{tariff['ap_price']:,}")
    
    print(f"\n📋 Pricing Rules Summary:")
    for rule in tariff_data['pricing_rules']:
        date_range = "Always applicable"
        if rule['start_date'] and rule['end_date']:
            date_range = f"{rule['start_date']} to {rule['end_date']}"
        elif rule['start_date']:
            date_range = f"From {rule['start_date']}"
        
        room_scope = "All rooms" if not rule['applicable_room_categories'] else ", ".join(rule['applicable_room_categories'])
        plan_scope = "All plans" if not rule['applicable_meal_plans'] else ", ".join(rule['applicable_meal_plans'])
        
        print(f"   • {rule['rule_name']}")
        print(f"     Date Range: {date_range}")
        print(f"     Room Categories: {room_scope}")
        print(f"     Meal Plans: {plan_scope}")
        print(f"     Type: {rule['rule_type']} ({rule['charge_type']})")
        print()

def main():
    """Main demonstration function."""
    try:
        test_pricing_scenarios()
        demonstrate_rule_integration()
        
        print(f"\n\n✅ Demo completed successfully!")
        print("💡 Key Features Demonstrated:")
        print("   • Room category-specific pricing rules")
        print("   • Date range-based seasonal rates")
        print("   • Meal plan-specific discounts")
        print("   • Percentage and fixed amount calculations")
        print("   • Extra person charges")
        print("   • Mandatory fees and taxes")
        print("   • Detailed pricing breakdowns")
        
    except Exception as e:
        print(f"❌ Demo failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
