#!/usr/bin/env python3
"""
Direct fix script for PDF extraction service on Linode
This will deploy the service safely without affecting other services
"""

import subprocess
import time
import requests

SERVER_IP = "*************"

def run_ssh_command(command, timeout=60):
    """Run SSH command on the server"""
    ssh_cmd = f'ssh root@{SERVER_IP} "{command}"'
    try:
        result = subprocess.run(ssh_cmd, shell=True, capture_output=True, text=True, timeout=timeout)
        return result.returncode == 0, result.stdout, result.stderr
    except subprocess.TimeoutExpired:
        return False, "", "Command timed out"
    except Exception as e:
        return False, "", str(e)

def check_service_status():
    """Check if the PDF extraction service is running"""
    try:
        response = requests.get(f"http://{SERVER_IP}:5000/", timeout=10)
        return response.status_code == 200
    except:
        return False

def deploy_pdf_service():
    """Deploy the PDF extraction service"""
    print("🚀 Deploying PDF Extraction Service on Linode")
    print("=" * 50)
    
    # Step 1: Check current status
    print("📊 Checking current service status...")
    if check_service_status():
        print("✅ Service is already running!")
        return True
    else:
        print("❌ Service is not running, deploying...")
    
    # Step 2: Clone/update repository
    print("\n📥 Updating repository...")
    commands = [
        "cd /root",
        "rm -rf tariff-extraction",
        "git clone -b hotel-tariff https://github.com/tripmilestone/Tripmilestone-admin-frontend.git tariff-extraction"
    ]
    
    for cmd in commands:
        success, stdout, stderr = run_ssh_command(cmd)
        if not success:
            print(f"❌ Failed: {cmd}")
            print(f"Error: {stderr}")
            return False
        print(f"✅ {cmd}")
    
    # Step 3: Check if Docker is available
    print("\n🐳 Checking Docker availability...")
    success, stdout, stderr = run_ssh_command("docker --version")
    
    if not success:
        print("📦 Installing Docker...")
        install_commands = [
            "curl -fsSL https://get.docker.com -o get-docker.sh",
            "sh get-docker.sh",
            "curl -L 'https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m)' -o /usr/local/bin/docker-compose",
            "chmod +x /usr/local/bin/docker-compose"
        ]
        
        for cmd in install_commands:
            success, stdout, stderr = run_ssh_command(cmd, timeout=120)
            if not success:
                print(f"❌ Failed to install Docker: {cmd}")
                print(f"Error: {stderr}")
                # Fall back to PM2 deployment
                return deploy_with_pm2()
            print(f"✅ {cmd}")
    
    # Step 4: Deploy with Docker
    print("\n🐳 Deploying with Docker...")
    docker_commands = [
        "cd /root/tariff-extraction/Backend",
        "chmod +x deploy-docker.sh",
        "./deploy-docker.sh"
    ]
    
    for cmd in docker_commands:
        success, stdout, stderr = run_ssh_command(cmd, timeout=300)
        if not success:
            print(f"❌ Docker deployment failed: {cmd}")
            print(f"Error: {stderr}")
            print(f"Output: {stdout}")
            # Fall back to PM2 deployment
            return deploy_with_pm2()
        print(f"✅ {cmd}")
    
    # Step 5: Verify deployment
    print("\n🔍 Verifying deployment...")
    time.sleep(10)
    
    if check_service_status():
        print("✅ PDF Extraction service is running successfully!")
        return True
    else:
        print("❌ Service verification failed, trying PM2 fallback...")
        return deploy_with_pm2()

def deploy_with_pm2():
    """Fallback deployment using PM2 with virtual environment"""
    print("\n🔄 Fallback: Deploying with PM2 and virtual environment...")
    
    # Create virtual environment to avoid conflicts
    venv_commands = [
        "cd /root/tariff-extraction/Backend",
        "python3 -m venv venv",
        "source venv/bin/activate && pip install --upgrade pip",
        "source venv/bin/activate && pip install -r requirements.txt"
    ]
    
    for cmd in venv_commands:
        success, stdout, stderr = run_ssh_command(cmd, timeout=120)
        if not success:
            print(f"❌ Failed: {cmd}")
            print(f"Error: {stderr}")
            return False
        print(f"✅ {cmd}")
    
    # Create environment file
    env_setup = '''
cd /root/tariff-extraction/Backend
cat > .env << 'EOF'
GOOGLE_API_KEY=your_google_api_key_here
MISTRAL_API_KEY=your_mistral_api_key_here
FLASK_ENV=production
FLASK_DEBUG=False
EOF
'''
    
    success, stdout, stderr = run_ssh_command(env_setup)
    if success:
        print("✅ Environment file created")
    
    # Stop existing PM2 process
    run_ssh_command("pm2 stop tariff-extraction 2>/dev/null || true")
    run_ssh_command("pm2 delete tariff-extraction 2>/dev/null || true")
    
    # Start with PM2 using virtual environment
    pm2_start = "cd /root/tariff-extraction/Backend && pm2 start app.py --name tariff-extraction --interpreter ./venv/bin/python"
    success, stdout, stderr = run_ssh_command(pm2_start)
    
    if success:
        run_ssh_command("pm2 save")
        print("✅ Service started with PM2")
        
        # Verify
        time.sleep(5)
        if check_service_status():
            print("✅ PM2 deployment successful!")
            return True
    
    print("❌ PM2 deployment also failed")
    return False

def show_status():
    """Show current service status"""
    print("\n📊 Current Service Status:")
    
    # Check if service responds
    if check_service_status():
        print("✅ PDF Extraction Service: ONLINE")
    else:
        print("❌ PDF Extraction Service: OFFLINE")
    
    # Show PM2 status
    success, stdout, stderr = run_ssh_command("pm2 status")
    if success:
        print("\n📋 PM2 Status:")
        print(stdout)
    
    # Show Docker status
    success, stdout, stderr = run_ssh_command("docker ps | grep tariff")
    if success and stdout.strip():
        print("\n🐳 Docker Status:")
        print(stdout)

def main():
    """Main function"""
    print("🔧 PDF Extraction Service - Emergency Fix")
    print("=" * 50)
    
    # Deploy the service
    if deploy_pdf_service():
        print("\n🎉 PDF Extraction Service deployed successfully!")
        print(f"🌐 Service URL: http://{SERVER_IP}:5000")
        print("🔧 Your frontend should now detect the service")
    else:
        print("\n❌ Deployment failed. Manual intervention required.")
        print(f"\n📋 Manual steps:")
        print(f"1. SSH to server: ssh root@{SERVER_IP}")
        print(f"2. Check logs: pm2 logs tariff-extraction")
        print(f"3. Or check Docker: docker-compose logs")
    
    # Show final status
    show_status()

if __name__ == "__main__":
    main()
