import React, { useState } from 'react';
import { StructuredTariffData } from '@/types/types';
import { Calculator, Calendar, DollarSign, MapPin } from 'lucide-react';
import toast from 'react-hot-toast';

const PDF_EXTRACTION_API_URL = import.meta.env.VITE_PDF_EXTRACTION_API_URL || 'http://localhost:5000/api';

interface PricingCalculatorProps {
  data: StructuredTariffData;
}

interface PricingResult {
  success: boolean;
  final_price?: number;
  details?: {
    room_category: string;
    meal_plan: string;
    check_in_date: string;
    currency: string;
  };
  error?: string;
}

const PricingCalculator: React.FC<PricingCalculatorProps> = ({ data }) => {
  const [selectedRoom, setSelectedRoom] = useState('');
  const [selectedMealPlan, setSelectedMealPlan] = useState('cp');
  const [checkInDate, setCheckInDate] = useState('');
  const [isCalculating, setIsCalculating] = useState(false);
  const [pricingResult, setPricingResult] = useState<PricingResult | null>(null);

  // Get unique room categories from the data
  const roomCategories = [...new Set(data.base_tariffs.map(tariff => tariff.room_category))];

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(price);
  };

  const formatRoomName = (roomCategory: string) => {
    return roomCategory
      .replace(/([A-Z])/g, ' $1')
      .trim()
      .replace(/\s+/g, ' ')
      .toLowerCase()
      .split(' ')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1))
      .join(' ');
  };

  const calculatePrice = async () => {
    if (!selectedRoom || !checkInDate) {
      toast.error('Please select a room and check-in date');
      return;
    }

    setIsCalculating(true);
    setPricingResult(null);

    try {
      const response = await fetch(`${PDF_EXTRACTION_API_URL}/calculate-price`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          structured_data: data,
          check_in_date: checkInDate,
          room_category: selectedRoom,
          meal_plan: selectedMealPlan,
        }),
      });

      const result: PricingResult = await response.json();

      if (result.success) {
        setPricingResult(result);
        toast.success(`Price calculated: ${formatPrice(result.final_price!)}`);
      } else {
        setPricingResult(result);
        toast.error(result.error || 'Failed to calculate price');
      }
    } catch (error) {
      console.error('Error calculating price:', error);
      toast.error('Failed to calculate price. Please try again.');
      setPricingResult({
        success: false,
        error: 'Network error occurred'
      });
    } finally {
      setIsCalculating(false);
    }
  };

  const getApplicableRules = () => {
    if (!checkInDate) return [];
    
    const checkIn = new Date(checkInDate);
    return data.pricing_rules.filter(rule => {
      if (!rule.start_date || !rule.end_date) return false;
      
      const start = new Date(rule.start_date);
      const end = new Date(rule.end_date);
      
      return checkIn >= start && checkIn <= end;
    });
  };

  const getBasePrice = () => {
    if (!selectedRoom) return null;
    
    const tariff = data.base_tariffs.find(t => 
      t.room_category.toLowerCase().includes(selectedRoom.toLowerCase())
    );
    
    if (!tariff) return null;
    
    const priceKey = `${selectedMealPlan}_price` as keyof typeof tariff;
    return tariff[priceKey] as number;
  };

  const applicableRules = getApplicableRules();
  const basePrice = getBasePrice();

  return (
    <div className="bg-white border border-gray-200 rounded-lg shadow-sm">
      <div className="px-6 py-4 border-b border-gray-200">
        <h3 className="text-lg font-semibold text-gray-900 flex items-center gap-2">
          <Calculator size={20} className="text-blue-600" />
          Pricing Calculator
        </h3>
        <p className="text-sm text-gray-600 mt-1">
          Calculate the final price for any date with automatic surcharge application
        </p>
      </div>

      <div className="px-6 py-4 space-y-4">
        {/* Room Selection */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            <MapPin size={16} className="inline mr-1" />
            Room Category
          </label>
          <select
            value={selectedRoom}
            onChange={(e) => setSelectedRoom(e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="">Select a room category</option>
            {roomCategories.map((room) => (
              <option key={room} value={room}>
                {formatRoomName(room)}
              </option>
            ))}
          </select>
        </div>

        {/* Meal Plan Selection */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            <DollarSign size={16} className="inline mr-1" />
            Meal Plan
          </label>
          <div className="grid grid-cols-3 gap-2">
            {[
              { value: 'cp', label: 'CP (Continental Plan)' },
              { value: 'map', label: 'MAP (Modified American Plan)' },
              { value: 'ap', label: 'AP (American Plan)' }
            ].map((plan) => (
              <button
                key={plan.value}
                onClick={() => setSelectedMealPlan(plan.value)}
                className={`px-3 py-2 text-sm rounded-md border transition-colors ${
                  selectedMealPlan === plan.value
                    ? 'bg-blue-600 text-white border-blue-600'
                    : 'bg-white text-gray-700 border-gray-300 hover:bg-gray-50'
                }`}
              >
                {plan.label}
              </button>
            ))}
          </div>
        </div>

        {/* Date Selection */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            <Calendar size={16} className="inline mr-1" />
            Check-in Date
          </label>
          <input
            type="date"
            value={checkInDate}
            onChange={(e) => setCheckInDate(e.target.value)}
            min={new Date().toISOString().split('T')[0]}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
        </div>

        {/* Base Price Preview */}
        {basePrice && (
          <div className="bg-gray-50 rounded-md p-3">
            <div className="text-sm text-gray-600">Base Price</div>
            <div className="text-lg font-semibold text-gray-900">{formatPrice(basePrice)}</div>
          </div>
        )}

        {/* Applicable Rules Preview */}
        {applicableRules.length > 0 && (
          <div className="bg-orange-50 rounded-md p-3">
            <div className="text-sm text-orange-800 font-medium mb-1">
              Applicable Surcharges for {checkInDate}:
            </div>
            {applicableRules.map((rule, index) => (
              <div key={index} className="text-sm text-orange-700">
                • {rule.rule_name}: +{formatPrice(rule.amount)}
              </div>
            ))}
          </div>
        )}

        {/* Calculate Button */}
        <button
          onClick={calculatePrice}
          disabled={!selectedRoom || !checkInDate || isCalculating}
          className="w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors"
        >
          {isCalculating ? 'Calculating...' : 'Calculate Final Price'}
        </button>

        {/* Result Display */}
        {pricingResult && (
          <div className={`rounded-md p-4 ${
            pricingResult.success ? 'bg-green-50 border border-green-200' : 'bg-red-50 border border-red-200'
          }`}>
            {pricingResult.success ? (
              <div>
                <div className="text-green-800 font-medium mb-2">Final Price Calculated</div>
                <div className="text-2xl font-bold text-green-900 mb-2">
                  {formatPrice(pricingResult.final_price!)}
                </div>
                <div className="text-sm text-green-700">
                  <div>Room: {formatRoomName(pricingResult.details!.room_category)}</div>
                  <div>Meal Plan: {pricingResult.details!.meal_plan}</div>
                  <div>Date: {new Date(pricingResult.details!.check_in_date).toLocaleDateString()}</div>
                </div>
              </div>
            ) : (
              <div>
                <div className="text-red-800 font-medium mb-1">Calculation Failed</div>
                <div className="text-sm text-red-700">{pricingResult.error}</div>
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
};

export default PricingCalculator;
