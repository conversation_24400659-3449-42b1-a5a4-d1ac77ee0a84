#!/usr/bin/env python3
"""
Simple test script to debug the extraction issue.
"""

import os
import sys
import json

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_basic_imports():
    """Test if we can import the basic modules."""
    print("🔍 Testing basic imports...")
    
    try:
        import extract_tariff
        print("✅ extract_tariff imported successfully")
        return True
    except Exception as e:
        print(f"❌ Failed to import extract_tariff: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_environment_variables():
    """Test if environment variables are accessible."""
    print("\n🔍 Testing environment variables...")
    
    # Load dotenv
    try:
        from dotenv import load_dotenv
        load_dotenv()
        print("✅ dotenv loaded")
    except Exception as e:
        print(f"❌ Failed to load dotenv: {e}")
        return False
    
    # Check API keys
    mistral_key = os.getenv("MISTRAL_API_KEY")
    google_key = os.getenv("GOOGLE_API_KEY")
    
    print(f"MISTRAL_API_KEY: {'✅ Found' if mistral_key else '❌ Missing'}")
    print(f"GOOGLE_API_KEY: {'✅ Found' if google_key else '❌ Missing'}")
    
    if mistral_key:
        print(f"MISTRAL_API_KEY starts with: {mistral_key[:10]}...")
    if google_key:
        print(f"GOOGLE_API_KEY starts with: {google_key[:10]}...")
    
    return bool(mistral_key and google_key)

def test_with_sample_text():
    """Test the extraction with sample text instead of PDF."""
    print("\n🔍 Testing with sample text...")
    
    try:
        from extract_tariff import orchestrate_tariff_extraction, safe_json_loads, call_llm
        from langchain_google_genai import ChatGoogleGenerativeAI
        from langchain_core.pydantic_v1 import SecretStr
        
        google_api_key = os.getenv("GOOGLE_API_KEY")
        if not google_api_key:
            print("❌ No Google API key found")
            return False
        
        # Create LLM client
        llm_client = ChatGoogleGenerativeAI(
            model="gemini-1.5-flash",
            api_key=SecretStr(google_api_key)
        )
        print("✅ LLM client created")
        
        # Sample hotel tariff text
        sample_text = """
        HOTEL TARIFF SHEET
        
        Room Category | CP Plan | MAP Plan | AP Plan
        Deluxe Room   | 5000    | 6000     | 7000
        Suite Room    | 8000    | 9000     | 10000
        
        SEASONAL CHARGES:
        Christmas Period (24-Dec to 26-Dec): 20% extra charge
        New Year Period (31-Dec to 2-Jan): 25% extra charge
        
        EXTRA PERSON POLICY:
        Extra Adult: Rs. 1500 per person per night
        Extra Child (5-12 years): Rs. 800 per person per night
        """
        
        print("📝 Testing with sample hotel tariff text...")
        result = orchestrate_tariff_extraction(sample_text, llm_client)
        
        print(f"📊 Result type: {type(result)}")
        print(f"📊 Result: {result}")
        
        if result:
            print("✅ Sample text extraction successful!")
            return True
        else:
            print("❌ Sample text extraction failed")
            return False
            
    except Exception as e:
        print(f"❌ Sample text test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main test function."""
    print("🚀 Simple Extraction Test")
    print("=" * 30)
    
    # Test 1: Basic imports
    if not test_basic_imports():
        print("❌ Basic imports failed. Cannot continue.")
        return
    
    # Test 2: Environment variables
    if not test_environment_variables():
        print("❌ Environment variables not set. Cannot continue.")
        return
    
    # Test 3: Sample text extraction
    if test_with_sample_text():
        print("\n✅ All tests passed! The extraction system should work.")
    else:
        print("\n❌ Sample text extraction failed. Check the logs above.")
    
    print("\n🎯 Test complete!")

if __name__ == "__main__":
    main()
