# Tariff Extraction System Upgrade

## Overview
The tariff extraction system has been upgraded with a new multi-step LLM orchestration approach that provides more structured and reliable data extraction from hotel tariff PDFs.

## Key Changes

### 1. New Helper Functions
- `safe_json_loads()`: Safely parses JSON responses from LLMs, handling markdown formatting
- `call_llm()`: Generic function to call LLM clients and handle responses

### 2. Multi-Step Extraction Process
The new system uses a 3-step approach:

#### Step 1: Triage and Component Extraction
- Uses `TRIAGE_PROMPT_TEMPLATE` to segment the document into components:
  - `base_tariff_tables`: Main pricing tables
  - `seasonal_surcharges`: Peak season price hikes
  - `mandatory_add_ons`: Compulsory charges (gala dinners, etc.)
  - `extra_person_policy`: Extra adult/child pricing rules
  - `validity_period`: Date ranges for tariff validity

#### Step 2a: Base Tariff Extraction
- Uses `BASE_TARIFF_PROMPT_TEMPLATE` to extract room categories and meal plan prices
- Returns structured JSON with: `room_category`, `cp_price`, `map_price`, `ap_price`

#### Step 2b: Pricing Rules Extraction
- Uses `RULES_EXTRACTION_PROMPT_TEMPLATE` to extract pricing rules
- Supports three rule types:
  - `SEASONAL_HIKE`: Percentage/flat rate increases
  - `MANDATORY_ADD_ON`: Compulsory charges
  - `EXTRA_PERSON`: Extra adult/child costs

### 3. Updated Function Signatures

#### Old Function
```python
extract_tariff_from_pdf(pdf_path, use_llm=True)
# Returns: List of dictionaries (old format)
```

#### New Function
```python
extract_tariff_from_pdf(pdf_path)
# Returns: Dictionary with structured data
{
  "base_tariffs": [
    {
      "room_category": "Deluxe Room",
      "cp_price": 5000,
      "map_price": 6000,
      "ap_price": 7000
    }
  ],
  "pricing_rules": [
    {
      "rule_name": "Christmas Hike",
      "start_date": "2025-12-24",
      "end_date": "2025-12-26",
      "charge_type": "per_room",
      "amount": 1000
    }
  ]
}
```

### 4. API Response Changes
The `/api/extract-tariff` endpoint now returns:
```json
{
  "success": true,
  "data": {
    "base_tariffs": [...],
    "pricing_rules": [...]
  },
  "count": 15,
  "base_tariffs_count": 10,
  "pricing_rules_count": 5
}
```

## Benefits

1. **Structured Output**: Clear separation between base tariffs and pricing rules
2. **Better Error Handling**: Robust JSON parsing with fallback mechanisms
3. **Improved Accuracy**: Multi-step approach reduces extraction errors
4. **Standardized Format**: Consistent data structure for frontend consumption
5. **Rule-Based Pricing**: Explicit handling of seasonal surcharges and add-ons

## Usage

### Testing the New System
```bash
# Run the test script
python Backend/test_new_extraction.py path/to/your/tariff.pdf

# Or use the default test file
python Backend/test_new_extraction.py
```

### API Usage
```bash
# Upload a PDF file to the API
curl -X POST http://localhost:5000/api/extract-tariff \
  -F "file=@your_tariff.pdf"
```

### Integration in Frontend
The frontend should now expect the new data structure:
```javascript
// Handle the API response
const response = await fetch('/api/extract-tariff', {
  method: 'POST',
  body: formData
});

const result = await response.json();
if (result.success) {
  const baseTariffs = result.data.base_tariffs;
  const pricingRules = result.data.pricing_rules;
  
  // Process base tariffs
  baseTariffs.forEach(tariff => {
    console.log(`${tariff.room_category}: CP=${tariff.cp_price}`);
  });
  
  // Process pricing rules
  pricingRules.forEach(rule => {
    console.log(`${rule.rule_name}: ${rule.amount} (${rule.charge_type})`);
  });
}
```

## Migration Notes

1. **Backward Compatibility**: The old extraction functions are still present but deprecated
2. **Data Format**: Frontend code needs to be updated to handle the new structured format
3. **Error Handling**: The new system provides better error messages and debugging info
4. **Performance**: The multi-step approach may take slightly longer but provides more accurate results

## Files Modified

- `Backend/extract_tariff.py`: Core extraction logic updated
- `Backend/app.py`: API endpoint updated to handle new return format
- `Backend/test_new_extraction.py`: New test script (added)
- `Backend/EXTRACTION_UPGRADE_SUMMARY.md`: This documentation (added)

## Next Steps

1. Test the new system with various tariff PDFs
2. Update frontend components to use the new data structure
3. Add validation for the extracted data
4. Consider adding configuration options for the LLM prompts
5. Implement caching for frequently processed documents
