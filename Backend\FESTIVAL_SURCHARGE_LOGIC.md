# Festival & Peak Season Surcharge Logic

## Overview

This document explains the corrected logic for festival and peak season surcharges, ensuring they apply only to room rates and not to individual person charges.

## Key Principle

**Festival/Peak season surcharges apply ONLY to the base room rate, NOT to extra person charges.**

## Pricing Calculation Order

The enhanced pricing engine now follows this specific order:

### 1. **Room-Based Calculations** (Step 2a)
- Base room price (CP/MAP/AP)
- Seasonal surcharges (Christmas, New Year, etc.)
- Weekend surcharges
- Room-specific discounts

### 2. **Person-Based Calculations** (Step 2b)
- Extra adult charges (NOT affected by seasonal surcharges)
- Extra child charges (NOT affected by seasonal surcharges)

### 3. **Mandatory Fees** (Step 2c)
- Taxes (GST) calculated on: Room Total + Extra Person Charges
- Service fees and resort fees

## Example Demonstration

### Scenario: Christmas Peak Season
- **Date**: December 25, 2025
- **Room**: Deluxe Room (MAP ₹6,000)
- **Guests**: 4 adults, 2 children
- **Peak Season Surcharge**: 30%

### ❌ Incorrect Logic (Old Way)
```
Base Room Price: ₹6,000
Peak Season Surcharge (30%): ₹1,800
Extra Adults (4 × ₹1,500): ₹6,000
Peak Season on Extra Adults (30%): ₹1,800  ← WRONG!
Extra Children (2 × ₹800): ₹1,600
Peak Season on Extra Children (30%): ₹480  ← WRONG!
GST (18%): ₹3,222
Total: ₹20,902
```

### ✅ Correct Logic (New Way)
```
Step 1 - Room Calculations:
Base Room Price: ₹6,000
Peak Season Surcharge (30%): ₹1,800
Room Total: ₹7,800

Step 2 - Person Calculations:
Extra Adults (4 × ₹1,500): ₹6,000  ← No surcharge applied
Extra Children (2 × ₹800): ₹1,600  ← No surcharge applied
Person Total: ₹7,600

Step 3 - Mandatory Fees:
Subtotal (Room + Person): ₹15,400
GST (18% on subtotal): ₹2,772

Final Total: ₹18,172
```

## Business Logic Rationale

### Why This Makes Sense

1. **Room Rates**: Seasonal demand affects room availability and base pricing
2. **Extra Person Charges**: These are service-based charges (extra bed, meals, etc.) that don't fluctuate with seasonal demand
3. **Taxes**: Applied to the total service value (room + person charges)

### Real-World Examples

**Christmas Week at a Resort:**
- Base room rate increases due to high demand → Surcharge applies
- Extra bed setup cost remains the same → No surcharge
- Extra meal cost remains the same → No surcharge
- Government taxes apply to total bill → Calculated on final subtotal

## Implementation Details

### Rule Processing Order

```python
# Step 2a: Room-based rules (surcharges, discounts, seasonal rates)
for rule in pricing_rules:
    if rule_type in ['surcharge', 'seasonal_rate', 'discount']:
        # Apply to base room price only
        
# Step 2b: Person-based rules (extra adults, children)
for rule in pricing_rules:
    if rule_type == 'extra_person':
        # Apply fixed per-person charges (no seasonal multiplier)
        
# Step 2c: Mandatory fees (taxes, service charges)
for rule in pricing_rules:
    if rule_type == 'mandatory_fee':
        # Calculate on room_total + person_total
```

### Rule Configuration

**Festival Surcharge (Correct)**:
```json
{
  "rule_name": "Christmas Peak Season",
  "start_date": "2025-12-24",
  "end_date": "2026-01-02",
  "charge_type": "percentage",
  "amount": 0.30,
  "rule_type": "seasonal_rate"  ← Applies to room only
}
```

**Extra Person Charge (Separate)**:
```json
{
  "rule_name": "Extra Adult Charge",
  "start_date": null,
  "end_date": null,
  "charge_type": "per_adult",
  "amount": 1500,
  "rule_type": "extra_person"  ← Fixed rate, no seasonal multiplier
}
```

## API Response Format

The corrected pricing breakdown clearly separates:

```json
{
  "pricing": {
    "base_price": 6000,
    "surcharges": 1800,      ← Room-based only
    "discounts": 0,
    "extra_person_charges": 7600,  ← Separate, no seasonal multiplier
    "mandatory_fees": 2772,   ← Calculated on subtotal
    "final_price": 18172
  },
  "applied_rules": [
    {
      "name": "Christmas Peak Season",
      "type": "seasonal_rate",
      "amount": 1800,
      "description": "Christmas Peak Season: 30% surcharge (₹1,800)"
    },
    {
      "name": "Extra Adult Charge",
      "type": "extra_person", 
      "amount": 6000,
      "description": "Extra Adult Charge: ₹1,500 per adult (₹6,000 total)"
    }
  ]
}
```

## Benefits of Corrected Logic

1. **Accurate Pricing**: Reflects real-world hotel pricing practices
2. **Transparent Breakdown**: Clear separation of room vs. person charges
3. **Fair Pricing**: Extra person charges remain consistent regardless of season
4. **Tax Compliance**: Proper tax calculation on total service value
5. **Business Flexibility**: Room rates can be adjusted seasonally while service charges remain stable

## Testing

The corrected logic has been tested with multiple scenarios:
- Regular weekdays
- Weekend surcharges
- Peak season periods
- Special discount periods
- Various guest configurations

All scenarios now properly apply festival surcharges only to room rates, not to individual person charges.
