#!/usr/bin/env python3
"""
Test script to verify the Flask API endpoint is working correctly.
"""

import requests
import json
import os

def test_api_endpoint():
    """Test the /api/extract-tariff endpoint."""
    print("🚀 Testing Flask API Endpoint")
    print("=" * 40)
    
    # API endpoint
    url = "http://127.0.0.1:5000/api/extract-tariff"
    
    # Create a simple test PDF content (this is just for testing the endpoint)
    # In a real scenario, you would use an actual PDF file
    test_pdf_content = b"%PDF-1.4\n1 0 obj\n<<\n/Type /Catalog\n/Pages 2 0 R\n>>\nendobj\n2 0 obj\n<<\n/Type /Pages\n/Kids [3 0 R]\n/Count 1\n>>\nendobj\n3 0 obj\n<<\n/Type /Page\n/Parent 2 0 R\n/MediaBox [0 0 612 792]\n>>\nendobj\nxref\n0 4\n0000000000 65535 f \n0000000009 00000 n \n0000000074 00000 n \n0000000120 00000 n \ntrailer\n<<\n/Size 4\n/Root 1 0 R\n>>\nstartxref\n179\n%%EOF"
    
    # Create a temporary PDF file for testing
    temp_pdf_path = "temp_test.pdf"
    with open(temp_pdf_path, "wb") as f:
        f.write(test_pdf_content)
    
    try:
        print("📤 Sending request to API...")
        
        # Prepare the file for upload
        with open(temp_pdf_path, "rb") as f:
            files = {
                'file': ('test.pdf', f, 'application/pdf')
            }
            
            # Send POST request
            response = requests.post(url, files=files, timeout=120)
        
        print(f"📥 Response Status: {response.status_code}")
        print(f"📥 Response Headers: {dict(response.headers)}")
        
        if response.status_code == 200:
            try:
                result = response.json()
                print("✅ API request successful!")
                print(f"📊 Response type: {type(result)}")
                print(f"📊 Response keys: {list(result.keys()) if isinstance(result, dict) else 'Not a dict'}")
                
                # Pretty print the response
                print("\n📋 Full Response:")
                print(json.dumps(result, indent=2, ensure_ascii=False))
                
                # Check if it's an error response
                if 'error' in result:
                    print(f"\n⚠️ API returned an error: {result['error']}")
                else:
                    print("\n✅ API extraction completed successfully!")
                    
            except json.JSONDecodeError as e:
                print(f"❌ Failed to parse JSON response: {e}")
                print(f"Raw response: {response.text}")
                
        else:
            print(f"❌ API request failed with status {response.status_code}")
            print(f"Response: {response.text}")
            
    except requests.exceptions.RequestException as e:
        print(f"❌ Request failed: {e}")
        
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        import traceback
        traceback.print_exc()
        
    finally:
        # Clean up temporary file
        if os.path.exists(temp_pdf_path):
            os.remove(temp_pdf_path)
            print(f"🧹 Cleaned up temporary file: {temp_pdf_path}")

def test_server_health():
    """Test if the Flask server is running."""
    print("\n🏥 Testing server health...")
    
    try:
        response = requests.get("http://127.0.0.1:5000/", timeout=5)
        if response.status_code == 200:
            print("✅ Flask server is running and responding")
            return True
        else:
            print(f"⚠️ Flask server responded with status {response.status_code}")
            return False
    except requests.exceptions.RequestException as e:
        print(f"❌ Flask server is not responding: {e}")
        return False

def main():
    """Main test function."""
    print("🧪 Flask API Endpoint Test")
    print("=" * 50)

    # Test the extraction endpoint directly
    test_api_endpoint()

    print("\n🎯 Test complete!")

if __name__ == "__main__":
    main()
