name: Emergency Service Restore

on:
  workflow_dispatch:  # Manual trigger only

jobs:
  restore:
    runs-on: ubuntu-latest
    
    steps:
    - name: Emergency Service Restoration
      uses: appleboy/ssh-action@master
      with:
        host: ${{ secrets.HOST }}
        username: ${{ secrets.USERNAME }}
        key: ${{ secrets.SSH_PRIVATE_KEY }}
        port: 22
        script: |
          echo "🚨 Emergency Service Restoration Started"
          echo "======================================"
          
          # Check current PM2 status
          echo "📊 Current PM2 Status:"
          pm2 status
          
          # Update PM2 first
          echo "🔄 Updating PM2..."
          pm2 update
          
          # Stop problematic tariff-extraction service first
          echo "🛑 Stopping tariff-extraction service..."
          pm2 stop tariff-extraction 2>/dev/null || echo "tariff-extraction not running"
          pm2 delete tariff-extraction 2>/dev/null || echo "tariff-extraction not found"

          # Restart critical frontend services
          echo "🔄 Restarting critical services..."

          # Admin frontend
          pm2 restart admin-frontend || pm2 start admin-frontend

          # Production frontend
          pm2 restart prod-frontend || pm2 start prod-frontend

          # Backend services
          pm2 restart tripmilestone-backend || pm2 start tripmilestone-backend
          pm2 restart family-api || pm2 start family-api

          # Staging frontend
          pm2 restart stg-frontend || pm2 start stg-frontend
          
          # Save PM2 configuration
          pm2 save
          
          # Check nginx status
          echo "🌐 Checking nginx status..."
          systemctl status nginx --no-pager
          
          # Restart nginx
          echo "🔄 Restarting nginx..."
          systemctl restart nginx
          
          # Wait a moment
          sleep 5
          
          # Check if services are responding
          echo "🔍 Testing services..."
          
          # Test nginx
          curl -I http://localhost/ || echo "⚠️ Nginx not responding on port 80"
          
          # Test if admin frontend is accessible
          curl -I http://localhost:3000/ || echo "⚠️ Admin frontend not responding"
          
          # Show final status
          echo "📊 Final PM2 Status:"
          pm2 status
          
          echo "📊 Final nginx Status:"
          systemctl status nginx --no-pager
          
          echo "✅ Emergency restoration completed!"
          echo "🌐 Please check: https://admin.tripxplo.com/"
