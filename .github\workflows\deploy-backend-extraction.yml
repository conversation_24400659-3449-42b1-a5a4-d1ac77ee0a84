name: Deploy Backend PDF Extraction (Safe Docker)

on:
  push:
    branches: [ hotel-tariff ]

jobs:
  deploy:
    runs-on: ubuntu-latest

    steps:
    - name: Deploy Backend PDF Extraction with <PERSON>er (Safe)
      uses: appleboy/ssh-action@master
      with:
        host: ${{ secrets.HOST }}
        username: ${{ secrets.USERNAME }}
        key: ${{ secrets.SSH_PRIVATE_KEY }}
        port: 22
        script: |
          echo "🐳 Starting Safe Docker Deployment for PDF Extraction"
          echo "====================================================="

          # Navigate to deployment directory
          cd /root

          # Remove existing tariff-extraction folder if it exists
          if [ -d "tariff-extraction" ]; then
            echo "📁 Removing existing tariff-extraction folder..."
            rm -rf tariff-extraction
          fi

          # Clone the repository with hotel-tariff branch
          echo "📥 Cloning repository (hotel-tariff branch)..."
          git clone -b hotel-tariff https://github.com/tripmilestone/Tripmilestone-admin-frontend.git tariff-extraction

          # Navigate to the Backend directory
          cd tariff-extraction/Backend

          # Make deployment script executable
          chmod +x deploy-docker.sh

          # Run the safe Docker deployment
          echo "🚀 Running safe Docker deployment..."
          ./deploy-docker.sh

          echo "✅ Safe Docker deployment completed!"
          echo "🔒 PDF Extraction service is now isolated and won't affect other services"

