@echo off
echo 🚀 Pushing Backend Changes to hotel-tariff branch
echo ================================================

REM Check if we're in the right directory
if not exist "Backend\extract_tariff.py" (
    echo ❌ Error: Backend\extract_tariff.py not found
    echo Please run this script from the Tripmilestone-admin-frontend root directory
    pause
    exit /b 1
)

echo ✅ Found Backend directory

REM Check if git is available
git --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Error: Git is not installed or not in PATH
    echo Please install Git and try again
    pause
    exit /b 1
)

echo ✅ Git is available

REM Check current git status
echo 📋 Current git status:
git status

echo.
echo 🔄 Switching to hotel-tariff branch...
git checkout hotel-tariff
if errorlevel 1 (
    echo ⚠️ Branch doesn't exist locally, creating it...
    git checkout -b hotel-tariff
)

echo.
echo 📦 Adding all changes...
git add .

echo.
echo 📝 Committing changes...
git commit -m "Backend PDF Extraction: Enhanced with auto-deployment

✅ Updated LLM model to gemini-2.5-pro
✅ Enhanced date parsing for LaTeX and various formats  
✅ Fixed tariff extraction and date range parsing
✅ Added comprehensive requirements.txt
✅ Created GitHub Actions workflow for auto-deployment
✅ Added manual deployment script (deploy.sh)
✅ Added complete deployment documentation
✅ Updated repository URLs to tripmilestone organization

Features:
- Improved date range extraction from PDFs
- Better handling of LaTeX mathematical notation
- Support for multiple date formats (Upavan, Woodstock, etc.)
- Automatic deployment on push to hotel-tariff branch
- PM2 process management on server
- Comprehensive error handling and logging"

if errorlevel 1 (
    echo ⚠️ Nothing to commit or commit failed
    echo This might be normal if no changes were made
)

echo.
echo 🚀 Pushing to hotel-tariff branch...
git push origin hotel-tariff

if errorlevel 1 (
    echo ❌ Push failed. This might be due to:
    echo   1. Authentication issues
    echo   2. Remote repository not configured
    echo   3. Network issues
    echo.
    echo 🔧 Try these commands manually:
    echo   git remote -v
    echo   git remote set-url origin https://github.com/tripmilestone/Tripmilestone-admin-frontend.git
    echo   git push origin hotel-tariff
    pause
    exit /b 1
)

echo.
echo ✅ Successfully pushed to hotel-tariff branch!
echo.
echo 🎯 Next Steps:
echo   1. Check GitHub Actions: https://github.com/tripmilestone/Tripmilestone-admin-frontend/actions
echo   2. Monitor deployment progress
echo   3. Verify deployment on server: ssh root@*************
echo   4. Check application status: pm2 status tariff-extraction
echo.
echo 📋 Files pushed:
echo   ✅ Backend/extract_tariff.py (enhanced date parsing, gemini-2.5-pro)
echo   ✅ Backend/requirements.txt (comprehensive dependencies)
echo   ✅ Backend/deploy.sh (manual deployment script)
echo   ✅ Backend/DEPLOYMENT.md (complete documentation)
echo   ✅ .github/workflows/deploy-backend-extraction.yml (auto-deployment)
echo.
echo 🔗 Repository: https://github.com/tripmilestone/Tripmilestone-admin-frontend
echo 🌿 Branch: hotel-tariff
echo 🚀 Auto-deployment will trigger automatically!

pause
