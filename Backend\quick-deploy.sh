#!/bin/bash

# Quick deployment script for PDF extraction service
# Run this directly on your Linode server

echo "🚀 Quick PDF Extraction Service Deployment"
echo "=========================================="

# Navigate to root directory
cd /root

# Stop any existing tariff-extraction PM2 process
echo "🛑 Stopping existing services..."
pm2 stop tariff-extraction 2>/dev/null || echo "No existing PM2 process"
pm2 delete tariff-extraction 2>/dev/null || echo "No existing PM2 process to delete"

# Stop any existing Docker containers
docker-compose -f /root/tariff-extraction/Backend/docker-compose.yml down 2>/dev/null || echo "No existing Docker containers"

# Remove existing directory
echo "📁 Cleaning up existing installation..."
rm -rf tariff-extraction

# Clone fresh repository
echo "📥 Cloning latest code..."
git clone -b hotel-tariff https://github.com/tripmilestone/Tripmilestone-admin-frontend.git tariff-extraction

# Navigate to backend directory
cd tariff-extraction/Backend

# Create environment file if it doesn't exist
if [ ! -f ".env" ]; then
    echo "📝 Creating environment file..."
    cat > .env << 'EOF'
# Google AI API Key (for Gemini 2.5 Pro)
GOOGLE_API_KEY=your_google_api_key_here

# Mistral AI API Key (for OCR)
MISTRAL_API_KEY=your_mistral_api_key_here

# Flask Configuration
FLASK_ENV=production
FLASK_DEBUG=False
EOF
    echo "⚠️ Please update .env file with your actual API keys"
fi

# Try Docker deployment first
echo "🐳 Attempting Docker deployment..."
if command -v docker &> /dev/null && command -v docker-compose &> /dev/null; then
    echo "✅ Docker is available"
    
    # Make script executable
    chmod +x deploy-docker.sh
    
    # Try Docker deployment
    if ./deploy-docker.sh; then
        echo "✅ Docker deployment successful!"
        echo "🌐 Service should be running on port 5000"
        exit 0
    else
        echo "⚠️ Docker deployment failed, trying PM2..."
    fi
else
    echo "⚠️ Docker not available, using PM2..."
fi

# Fallback to PM2 with virtual environment
echo "🔄 Using PM2 with virtual environment..."

# Create virtual environment
echo "📦 Creating virtual environment..."
python3 -m venv venv

# Activate virtual environment and install dependencies
echo "📦 Installing dependencies..."
source venv/bin/activate
pip install --upgrade pip
pip install -r requirements.txt

# Start with PM2
echo "🚀 Starting service with PM2..."
pm2 start app.py --name tariff-extraction --interpreter ./venv/bin/python

# Save PM2 configuration
pm2 save

# Check if service is running
sleep 3
if pm2 list | grep -q "tariff-extraction.*online"; then
    echo "✅ Service started successfully with PM2!"
    
    # Test the service
    if curl -f http://localhost:5000/ 2>/dev/null; then
        echo "✅ Service is responding on port 5000"
    else
        echo "⚠️ Service may still be starting up"
    fi
    
    echo ""
    echo "📊 PM2 Status:"
    pm2 status
    
else
    echo "❌ Failed to start service"
    echo "📋 Checking logs..."
    pm2 logs tariff-extraction --lines 10
    exit 1
fi

echo ""
echo "🎉 PDF Extraction Service deployment completed!"
echo "🌐 Service URL: http://localhost:5000"
echo "🔧 Your frontend should now detect the service"
echo ""
echo "📋 Management commands:"
echo "  - Check status: pm2 status"
echo "  - View logs: pm2 logs tariff-extraction"
echo "  - Restart: pm2 restart tariff-extraction"
echo "  - Stop: pm2 stop tariff-extraction"
