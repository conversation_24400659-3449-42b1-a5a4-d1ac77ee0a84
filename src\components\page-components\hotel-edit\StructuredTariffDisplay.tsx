import React from 'react';
import { StructuredTariffData } from '@/types/types';
// Simple Card components since they're not available in ui
const Card: React.FC<{ children: React.ReactNode; className?: string }> = ({ children, className = '' }) => (
  <div className={`bg-white border border-gray-200 rounded-lg shadow-sm ${className}`}>
    {children}
  </div>
);

const CardHeader: React.FC<{ children: React.ReactNode }> = ({ children }) => (
  <div className="px-6 py-4 border-b border-gray-200">
    {children}
  </div>
);

const CardTitle: React.FC<{ children: React.ReactNode; className?: string }> = ({ children, className = '' }) => (
  <h3 className={`text-lg font-semibold text-gray-900 ${className}`}>
    {children}
  </h3>
);

const CardContent: React.FC<{ children: React.ReactNode }> = ({ children }) => (
  <div className="px-6 py-4">
    {children}
  </div>
);
// Simple Badge component since it's not available in ui
const Badge: React.FC<{ variant?: string; children: React.ReactNode }> = ({ children }) => (
  <span className="inline-flex items-center px-2 py-1 text-xs font-medium rounded-full bg-gray-100 text-gray-800">
    {children}
  </span>
);
import { Calendar, DollarSign, Users, User, Baby } from 'lucide-react';

interface StructuredTariffDisplayProps {
  data: StructuredTariffData;
  selectedRoom?: string;
}

const StructuredTariffDisplay: React.FC<StructuredTariffDisplayProps> = ({ 
  data, 
  selectedRoom 
}) => {
  // Filter base tariffs by selected room if specified
  const filteredBaseTariffs = selectedRoom 
    ? data.base_tariffs.filter(tariff => 
        tariff.room_category.toLowerCase().includes(selectedRoom.toLowerCase())
      )
    : data.base_tariffs;

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(price);
  };

  const formatDate = (dateStr: string | null) => {
    if (!dateStr) return 'Not specified';
    try {
      return new Date(dateStr).toLocaleDateString('en-IN', {
        day: 'numeric',
        month: 'short',
        year: 'numeric'
      });
    } catch {
      return dateStr;
    }
  };

  const getChargeTypeIcon = (chargeType: string) => {
    switch (chargeType) {
      case 'per_room':
        return <DollarSign size={16} className="text-blue-600" />;
      case 'per_adult':
        return <User size={16} className="text-green-600" />;
      case 'per_child':
        return <Baby size={16} className="text-purple-600" />;
      default:
        return <Users size={16} className="text-gray-600" />;
    }
  };

  const getChargeTypeLabel = (chargeType: string) => {
    switch (chargeType) {
      case 'per_room':
        return 'Per Room';
      case 'per_adult':
        return 'Per Adult';
      case 'per_child':
        return 'Per Child';
      default:
        return chargeType;
    }
  };

  return (
    <div className="space-y-6">
      {/* Base Tariffs Section */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <DollarSign size={20} className="text-blue-600" />
            Base Room Tariffs
            <Badge variant="secondary">{filteredBaseTariffs.length} rooms</Badge>
          </CardTitle>
        </CardHeader>
        <CardContent>
          {filteredBaseTariffs.length > 0 ? (
            <div className="overflow-x-auto">
              <table className="w-full border-collapse">
                <thead>
                  <tr className="border-b border-gray-200">
                    <th className="text-left py-3 px-4 font-medium text-gray-700">Room Category</th>
                    <th className="text-right py-3 px-4 font-medium text-gray-700">CP (Continental Plan)</th>
                    <th className="text-right py-3 px-4 font-medium text-gray-700">MAP (Modified American Plan)</th>
                    <th className="text-right py-3 px-4 font-medium text-gray-700">AP (American Plan)</th>
                  </tr>
                </thead>
                <tbody>
                  {filteredBaseTariffs.map((tariff, index) => (
                    <tr key={index} className="border-b border-gray-100 hover:bg-gray-50">
                      <td className="py-3 px-4 font-medium text-gray-900">
                        {tariff.room_category.replace(/([A-Z])/g, ' $1').trim()}
                      </td>
                      <td className="py-3 px-4 text-right">
                        {tariff.cp_price > 0 ? (
                          <span className="font-semibold text-green-600">
                            {formatPrice(tariff.cp_price)}
                          </span>
                        ) : (
                          <span className="text-gray-400">Not available</span>
                        )}
                      </td>
                      <td className="py-3 px-4 text-right">
                        {tariff.map_price > 0 ? (
                          <span className="font-semibold text-blue-600">
                            {formatPrice(tariff.map_price)}
                          </span>
                        ) : (
                          <span className="text-gray-400">Not available</span>
                        )}
                      </td>
                      <td className="py-3 px-4 text-right">
                        {tariff.ap_price > 0 ? (
                          <span className="font-semibold text-purple-600">
                            {formatPrice(tariff.ap_price)}
                          </span>
                        ) : (
                          <span className="text-gray-400">Not available</span>
                        )}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          ) : (
            <div className="text-center py-8 text-gray-500">
              {selectedRoom 
                ? `No tariffs found for "${selectedRoom}". Try selecting a different room.`
                : 'No base tariffs found in the extracted data.'
              }
            </div>
          )}
        </CardContent>
      </Card>

      {/* Pricing Rules Section */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Calendar size={20} className="text-orange-600" />
            Pricing Rules & Surcharges
            <Badge variant="secondary">{data.pricing_rules.length} rules</Badge>
          </CardTitle>
        </CardHeader>
        <CardContent>
          {data.pricing_rules.length > 0 ? (
            <div className="grid gap-4">
              {data.pricing_rules.map((rule, index) => (
                <div key={index} className="border border-gray-200 rounded-lg p-4 hover:bg-gray-50">
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <h4 className="font-semibold text-gray-900 mb-2">{rule.rule_name}</h4>
                      <div className="flex items-center gap-4 text-sm text-gray-600">
                        <div className="flex items-center gap-1">
                          <Calendar size={14} />
                          <span>
                            {formatDate(rule.start_date)} - {formatDate(rule.end_date)}
                          </span>
                        </div>
                        <div className="flex items-center gap-1">
                          {getChargeTypeIcon(rule.charge_type)}
                          <span>{getChargeTypeLabel(rule.charge_type)}</span>
                        </div>
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="text-lg font-bold text-orange-600">
                        {formatPrice(rule.amount)}
                      </div>
                      <div className="text-xs text-gray-500">
                        {getChargeTypeLabel(rule.charge_type)}
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-8 text-gray-500">
              No pricing rules found in the extracted data.
            </div>
          )}
        </CardContent>
      </Card>

      {/* Summary Section */}
      <Card>
        <CardHeader>
          <CardTitle className="text-sm font-medium text-gray-700">Extraction Summary</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div>
              <span className="text-gray-600">Base Tariffs:</span>
              <span className="ml-2 font-semibold">{data.base_tariffs.length} room categories</span>
            </div>
            <div>
              <span className="text-gray-600">Pricing Rules:</span>
              <span className="ml-2 font-semibold">{data.pricing_rules.length} rules</span>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default StructuredTariffDisplay;
