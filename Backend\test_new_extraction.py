#!/usr/bin/env python3
"""
Test script for the new tariff extraction system.
This script demonstrates how to use the updated extract_tariff_from_pdf function.
"""

import os
import sys
import json
from extract_tariff import extract_tariff_from_pdf

def test_extraction(pdf_path):
    """Test the new extraction system with a PDF file."""
    print(f"Testing extraction with: {pdf_path}")
    
    if not os.path.exists(pdf_path):
        print(f"Error: PDF file not found at {pdf_path}")
        return
    
    try:
        # Call the new extraction function
        result = extract_tariff_from_pdf(pdf_path)
        
        print("\n" + "="*50)
        print("EXTRACTION RESULTS")
        print("="*50)
        
        if result:
            print(f"✅ Extraction successful!")
            print(f"📊 Base Tariffs: {len(result.get('base_tariffs', []))}")
            print(f"📋 Pricing Rules: {len(result.get('pricing_rules', []))}")
            
            # Display base tariffs
            if result.get('base_tariffs'):
                print("\n🏨 BASE TARIFFS:")
                for i, tariff in enumerate(result['base_tariffs'][:3], 1):  # Show first 3
                    print(f"  {i}. {tariff}")
                if len(result['base_tariffs']) > 3:
                    print(f"  ... and {len(result['base_tariffs']) - 3} more")
            
            # Display pricing rules
            if result.get('pricing_rules'):
                print("\n💰 PRICING RULES:")
                for i, rule in enumerate(result['pricing_rules'][:3], 1):  # Show first 3
                    print(f"  {i}. {rule}")
                if len(result['pricing_rules']) > 3:
                    print(f"  ... and {len(result['pricing_rules']) - 3} more")
            
            # Save results to JSON file
            output_file = f"output/test_results_{os.path.basename(pdf_path)}.json"
            os.makedirs('output', exist_ok=True)
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(result, f, indent=2, ensure_ascii=False)
            print(f"\n💾 Results saved to: {output_file}")
            
        else:
            print("❌ Extraction failed - no data returned")
            
    except Exception as e:
        print(f"❌ Error during extraction: {e}")
        import traceback
        traceback.print_exc()

def main():
    """Main function to run the test."""
    print("🚀 New Tariff Extraction System Test")
    print("="*40)
    
    # Check if PDF path is provided as argument
    if len(sys.argv) > 1:
        pdf_path = sys.argv[1]
    else:
        # Default test file (you can change this path)
        pdf_path = "test_tariff.pdf"
        print(f"No PDF path provided. Using default: {pdf_path}")
        print("Usage: python test_new_extraction.py <path_to_pdf>")
    
    test_extraction(pdf_path)

if __name__ == "__main__":
    main()
