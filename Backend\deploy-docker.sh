#!/bin/bash

# Safe Docker-based deployment script for PDF Extraction Service
# This won't interfere with existing PM2 services

echo "🐳 Starting Safe Docker Deployment for PDF Extraction"
echo "====================================================="

# Configuration
DEPLOY_DIR="/root/tariff-extraction"
SERVICE_NAME="tariff-extraction-service"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if running as root
if [ "$EUID" -ne 0 ]; then
    print_error "Please run this script as root"
    exit 1
fi

# Check if Docker is installed
if ! command -v docker &> /dev/null; then
    print_error "Docker is not installed. Installing Docker..."
    
    # Install Docker
    curl -fsSL https://get.docker.com -o get-docker.sh
    sh get-docker.sh
    
    # Install Docker Compose
    curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
    chmod +x /usr/local/bin/docker-compose
    
    print_success "Docker installed successfully"
fi

# Check if Docker Compose is available
if ! command -v docker-compose &> /dev/null; then
    print_error "Docker Compose not found. Installing..."
    curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
    chmod +x /usr/local/bin/docker-compose
fi

print_status "Docker and Docker Compose are available"

# Navigate to deployment directory
cd "$DEPLOY_DIR/Backend" || exit 1

# Stop existing container if running
print_status "Stopping existing container..."
docker-compose down 2>/dev/null || true

# Remove old container and images
print_status "Cleaning up old containers..."
docker container rm "$SERVICE_NAME" 2>/dev/null || true
docker image prune -f

# Check if .env file exists
if [ ! -f ".env" ]; then
    print_warning "Creating .env file template..."
    cat > .env << 'EOF'
# Google AI API Key (for Gemini 2.5 Pro)
GOOGLE_API_KEY=your_google_api_key_here

# Mistral AI API Key (for OCR)
MISTRAL_API_KEY=your_mistral_api_key_here

# Flask Configuration
FLASK_ENV=production
FLASK_DEBUG=False
EOF
    print_warning "Please update .env file with your actual API keys"
fi

# Build and start the container
print_status "Building and starting Docker container..."
if docker-compose up -d --build; then
    print_success "Container started successfully"
else
    print_error "Failed to start container"
    exit 1
fi

# Wait for container to be ready
print_status "Waiting for service to be ready..."
sleep 10

# Check if container is running
if docker-compose ps | grep -q "Up"; then
    print_success "✅ PDF Extraction service is running in Docker"
    
    # Test the service
    print_status "Testing the service..."
    if curl -f http://localhost:5000/ 2>/dev/null; then
        print_success "✅ Service is responding on port 5000"
    else
        print_warning "⚠️ Service may still be starting up"
    fi
    
    # Show container status
    print_status "Container status:"
    docker-compose ps
    
    # Show recent logs
    print_status "Recent logs:"
    docker-compose logs --tail=20
    
else
    print_error "❌ Container failed to start"
    print_status "Checking logs..."
    docker-compose logs
    exit 1
fi

print_success "🎉 Safe Docker deployment completed!"
echo ""
print_status "📋 Management Commands:"
echo "  - Check status: docker-compose ps"
echo "  - View logs: docker-compose logs -f"
echo "  - Restart: docker-compose restart"
echo "  - Stop: docker-compose down"
echo "  - Update: docker-compose up -d --build"
echo ""
print_status "🌐 Service URL: http://localhost:5000"
print_status "🔧 This deployment is isolated and won't affect other services"
