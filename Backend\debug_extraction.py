#!/usr/bin/env python3
"""
Debug script to test the extraction process step by step.
This helps identify where the extraction is failing.
"""

import os
import sys
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def check_environment():
    """Check if required environment variables are set."""
    print("🔍 Checking Environment Variables...")
    
    mistral_key = os.getenv("MISTRAL_API_KEY")
    google_key = os.getenv("GOOGLE_API_KEY")
    
    print(f"MISTRAL_API_KEY: {'✅ Set' if mistral_key else '❌ Missing'}")
    print(f"GOOGLE_API_KEY: {'✅ Set' if google_key else '❌ Missing'}")
    
    if not mistral_key or not google_key:
        print("\n❌ Missing required API keys!")
        print("Please set MISTRAL_API_KEY and GOOGLE_API_KEY in your .env file")
        return False
    
    return True

def test_imports():
    """Test if all required packages can be imported."""
    print("\n🔍 Testing Imports...")
    
    try:
        from mistralai import Mistral
        print("✅ Mistral import successful")
    except ImportError as e:
        print(f"❌ Mistral import failed: {e}")
        return False
    
    try:
        from langchain_google_genai import ChatGoogleGenerativeAI
        print("✅ LangChain Google GenAI import successful")
    except ImportError as e:
        print(f"❌ LangChain Google GenAI import failed: {e}")
        return False
    
    try:
        from extract_tariff import extract_tariff_from_pdf, extract_text_from_pdf
        print("✅ Extract tariff functions import successful")
    except ImportError as e:
        print(f"❌ Extract tariff import failed: {e}")
        return False
    
    return True

def test_ocr_only(pdf_path):
    """Test just the OCR step."""
    print(f"\n🔍 Testing OCR Only with: {pdf_path}")
    
    if not os.path.exists(pdf_path):
        print(f"❌ PDF file not found: {pdf_path}")
        return None
    
    try:
        from mistralai import Mistral
        from extract_tariff import extract_text_from_pdf
        
        api_key = os.getenv("MISTRAL_API_KEY")
        ocr_client = Mistral(api_key=api_key)
        
        print("📄 Starting OCR extraction...")
        text = extract_text_from_pdf(pdf_path, ocr_client)
        
        if text:
            print(f"✅ OCR successful! Extracted {len(text)} characters")
            print(f"📝 First 200 characters: {text[:200]}...")
            
            # Save OCR output
            os.makedirs('output', exist_ok=True)
            with open('output/debug_ocr_test.txt', 'w', encoding='utf-8') as f:
                f.write(text)
            print("💾 OCR output saved to output/debug_ocr_test.txt")
            
            return text
        else:
            print("❌ OCR failed - no text extracted")
            return None
            
    except Exception as e:
        print(f"❌ OCR test failed: {e}")
        import traceback
        traceback.print_exc()
        return None

def test_full_extraction(pdf_path):
    """Test the full extraction process."""
    print(f"\n🔍 Testing Full Extraction with: {pdf_path}")
    
    try:
        from extract_tariff import extract_tariff_from_pdf
        
        result = extract_tariff_from_pdf(pdf_path)
        
        print(f"📊 Extraction result type: {type(result)}")
        print(f"📊 Extraction result: {result}")
        
        if isinstance(result, dict) and "error" in result:
            print(f"❌ Extraction failed with error: {result['error']}")
            return False
        elif isinstance(result, dict):
            base_count = len(result.get('base_tariffs', []))
            rules_count = len(result.get('pricing_rules', []))
            print(f"✅ Extraction successful!")
            print(f"📊 Base tariffs: {base_count}")
            print(f"📊 Pricing rules: {rules_count}")
            return True
        else:
            print(f"❌ Unexpected result format: {type(result)}")
            return False
            
    except Exception as e:
        print(f"❌ Full extraction test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main debug function."""
    print("🚀 Tariff Extraction Debug Tool")
    print("=" * 40)
    
    # Step 1: Check environment
    if not check_environment():
        return
    
    # Step 2: Test imports
    if not test_imports():
        return
    
    # Step 3: Get PDF path
    if len(sys.argv) > 1:
        pdf_path = sys.argv[1]
    else:
        pdf_path = input("\n📁 Enter path to PDF file (or press Enter to skip): ").strip()
        if not pdf_path:
            print("No PDF path provided. Exiting.")
            return
    
    # Step 4: Test OCR only
    ocr_text = test_ocr_only(pdf_path)
    if not ocr_text:
        print("❌ OCR test failed. Cannot proceed with full extraction.")
        return
    
    # Step 5: Test full extraction
    test_full_extraction(pdf_path)
    
    print("\n🎯 Debug complete! Check the output/ directory for debug files.")

if __name__ == "__main__":
    main()
