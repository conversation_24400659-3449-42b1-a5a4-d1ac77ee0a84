#!/usr/bin/env python3
"""
Simple webhook server for auto-deployment
This runs on the server and listens for deployment triggers
"""

from flask import Flask, request, jsonify
import subprocess
import os
import threading
import time
from datetime import datetime

app = Flask(__name__)

# Configuration
DEPLOY_SCRIPT = "/root/tariff-extraction/Backend/auto-deploy-server.sh"
LOG_FILE = "/tmp/webhook-deploy.log"

def log_message(message):
    """Log messages with timestamp"""
    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    log_entry = f"[{timestamp}] {message}\n"
    
    # Print to console
    print(log_entry.strip())
    
    # Write to log file
    try:
        with open(LOG_FILE, "a") as f:
            f.write(log_entry)
    except Exception as e:
        print(f"Failed to write to log file: {e}")

def run_deployment():
    """Run the deployment script in a separate thread"""
    try:
        log_message("🚀 Starting deployment process...")
        
        # Make script executable
        subprocess.run(["chmod", "+x", DEPLOY_SCRIPT], check=True)
        
        # Run the deployment script
        result = subprocess.run(
            ["/bin/bash", DEPLOY_SCRIPT],
            capture_output=True,
            text=True,
            timeout=600  # 10 minutes timeout
        )
        
        if result.returncode == 0:
            log_message("✅ Deployment completed successfully!")
            log_message(f"Output: {result.stdout}")
        else:
            log_message(f"❌ Deployment failed with return code {result.returncode}")
            log_message(f"Error: {result.stderr}")
            log_message(f"Output: {result.stdout}")
            
    except subprocess.TimeoutExpired:
        log_message("⏰ Deployment timed out after 10 minutes")
    except Exception as e:
        log_message(f"💥 Deployment error: {str(e)}")

@app.route('/', methods=['GET'])
def health_check():
    """Health check endpoint"""
    return jsonify({
        "status": "ok",
        "service": "webhook-deploy",
        "timestamp": datetime.now().isoformat()
    })

@app.route('/deploy', methods=['POST'])
def trigger_deploy():
    """Trigger deployment endpoint"""
    try:
        # Log the deployment request
        log_message("📥 Deployment request received")
        
        # Get request info
        user_agent = request.headers.get('User-Agent', 'Unknown')
        remote_addr = request.remote_addr
        
        log_message(f"Request from: {remote_addr}, User-Agent: {user_agent}")
        
        # Start deployment in background thread
        deployment_thread = threading.Thread(target=run_deployment)
        deployment_thread.daemon = True
        deployment_thread.start()
        
        return jsonify({
            "status": "success",
            "message": "Deployment started",
            "timestamp": datetime.now().isoformat()
        }), 202
        
    except Exception as e:
        log_message(f"❌ Error processing deployment request: {str(e)}")
        return jsonify({
            "status": "error",
            "message": str(e),
            "timestamp": datetime.now().isoformat()
        }), 500

@app.route('/status', methods=['GET'])
def deployment_status():
    """Check deployment status"""
    try:
        # Check if PM2 process is running
        result = subprocess.run(
            ["pm2", "status", "tariff-extraction"],
            capture_output=True,
            text=True
        )
        
        pm2_running = result.returncode == 0
        
        # Get recent logs
        recent_logs = []
        try:
            with open(LOG_FILE, "r") as f:
                lines = f.readlines()
                recent_logs = lines[-10:]  # Last 10 lines
        except:
            recent_logs = ["No logs available"]
        
        return jsonify({
            "status": "ok",
            "pm2_running": pm2_running,
            "recent_logs": recent_logs,
            "timestamp": datetime.now().isoformat()
        })
        
    except Exception as e:
        return jsonify({
            "status": "error",
            "message": str(e),
            "timestamp": datetime.now().isoformat()
        }), 500

@app.route('/logs', methods=['GET'])
def get_logs():
    """Get deployment logs"""
    try:
        lines = int(request.args.get('lines', 50))
        
        logs = []
        try:
            with open(LOG_FILE, "r") as f:
                all_lines = f.readlines()
                logs = all_lines[-lines:]  # Last N lines
        except:
            logs = ["No logs available"]
        
        return jsonify({
            "status": "ok",
            "logs": logs,
            "timestamp": datetime.now().isoformat()
        })
        
    except Exception as e:
        return jsonify({
            "status": "error",
            "message": str(e),
            "timestamp": datetime.now().isoformat()
        }), 500

if __name__ == '__main__':
    log_message("🌐 Starting webhook deployment server...")
    log_message("Endpoints available:")
    log_message("  GET  / - Health check")
    log_message("  POST /deploy - Trigger deployment")
    log_message("  GET  /status - Check status")
    log_message("  GET  /logs - Get logs")
    
    # Run on port 5001 to avoid conflict with main app
    app.run(host='0.0.0.0', port=5001, debug=False)
