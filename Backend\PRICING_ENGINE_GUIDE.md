# 🎯 Pricing Engine Implementation Guide

## Overview
The pricing engine is the final component of the tariff extraction system. It takes the structured data from PDF extraction and calculates real-time prices for any date, room, and meal plan combination.

## 🏗️ Architecture

### 1. **Data Flow**
```
PDF Upload → OCR → LLM Extraction → Structured Data → Pricing Engine → Final Price
```

### 2. **Components**
- **Backend**: `calculate_final_price()` function in `extract_tariff.py`
- **API**: `/api/calculate-price` and `/api/demo-pricing` endpoints
- **Frontend**: `PricingCalculator` component
- **Testing**: `test_pricing_engine.py` script

## 🔧 Backend Implementation

### Core Function: `calculate_final_price()`

```python
def calculate_final_price(structured_data, check_in_date_str, room_category, meal_plan):
    """
    Calculates final price by applying base rates + applicable surcharges.
    
    Args:
        structured_data: Output from orchestrate_tariff_extraction()
        check_in_date_str: Date in "YYYY-MM-DD" format
        room_category: Room type (supports partial matching)
        meal_plan: 'cp', 'map', or 'ap'
    
    Returns:
        int: Final price in INR, or None if not found
    """
```

### How It Works

1. **Base Price Lookup**
   - Searches `base_tariffs` for matching room category
   - Uses partial string matching (e.g., "GREENVIEW" matches "GREENVIEWDELUXENON-AC")
   - Gets price for specified meal plan (cp_price, map_price, ap_price)

2. **Rule Application**
   - Iterates through `pricing_rules`
   - Checks if check-in date falls within rule's date range
   - Applies surcharges for `per_room` charge types
   - Supports single-date rules (start_date only) and date ranges

3. **Price Calculation**
   ```
   Final Price = Base Price + Applicable Surcharges
   ```

## 🌐 API Endpoints

### 1. `/api/calculate-price` (POST)
**Purpose**: Calculate price for specific parameters

**Request Body**:
```json
{
  "structured_data": { ... },
  "check_in_date": "2025-01-01",
  "room_category": "GREENVIEW",
  "meal_plan": "cp"
}
```

**Response**:
```json
{
  "success": true,
  "final_price": 3525,
  "details": {
    "room_category": "GREENVIEW",
    "meal_plan": "CP",
    "check_in_date": "2025-01-01",
    "currency": "INR"
  }
}
```

### 2. `/api/demo-pricing` (GET)
**Purpose**: Demonstrate pricing with sample Upavan Resort data

**Response**: Multiple pricing scenarios with explanations

## 🎨 Frontend Implementation

### PricingCalculator Component

**Features**:
- Room category dropdown (auto-populated from extracted data)
- Meal plan selection (CP/MAP/AP)
- Date picker with validation
- Base price preview
- Applicable surcharges preview
- Real-time price calculation
- Beautiful result display

**Usage**:
```tsx
<PricingCalculator data={structuredTariffData} />
```

## 🧪 Testing

### 1. **Backend Testing**
```bash
cd Backend
python test_pricing_engine.py
```

**Test Scenarios**:
- Regular dates (no surcharges)
- Peak season dates (with surcharges)
- Different room categories
- Different meal plans
- Invalid inputs

### 2. **API Testing**
```bash
# Demo endpoint
curl http://localhost:5000/api/demo-pricing

# Calculate specific price
curl -X POST http://localhost:5000/api/calculate-price \
  -H "Content-Type: application/json" \
  -d '{
    "structured_data": {...},
    "check_in_date": "2025-01-01",
    "room_category": "GREENVIEW",
    "meal_plan": "cp"
  }'
```

## 📊 Real Data Example (Upavan Resort)

### Base Tariffs
| Room Category | CP | MAP | AP |
|---------------|----|----|-----|
| Green View Deluxe Non-AC | ₹3,500 | ₹4,500 | ₹5,500 |
| Honeymoon Nest AC | ₹5,500 | ₹6,500 | ₹7,500 |
| Hill View Premium AC | ₹6,500 | ₹7,500 | ₹8,500 |

### Pricing Rules
1. **Seasonal Hike**: +₹25 per room (Dec 20, 2024 - Jan 15, 2025)
2. **New Year Gala Dinner**: +₹1,500 per adult, +₹1,200 per child

### Example Calculations

**Regular Date (March 15, 2025)**:
- Green View Deluxe Non-AC (CP): ₹3,500 (base only)

**Peak Season (January 1, 2025)**:
- Green View Deluxe Non-AC (CP): ₹3,525 (₹3,500 + ₹25 seasonal hike)

## 🚀 Usage Examples

### 1. **Hotel Booking System**
```javascript
// Calculate price for booking
const price = await calculatePrice({
  room: "GREENVIEW",
  mealPlan: "cp",
  checkIn: "2025-01-01",
  structuredData: extractedTariffData
});
```

### 2. **Price Comparison**
```javascript
// Compare prices across dates
const dates = ["2025-01-01", "2025-03-15", "2025-12-25"];
const prices = await Promise.all(
  dates.map(date => calculatePrice({...params, checkIn: date}))
);
```

### 3. **Dynamic Pricing Display**
```javascript
// Update prices when user changes selection
useEffect(() => {
  if (selectedRoom && selectedDate && selectedPlan) {
    calculateAndDisplayPrice();
  }
}, [selectedRoom, selectedDate, selectedPlan]);
```

## 🔮 Future Enhancements

### 1. **Advanced Features**
- Multi-night stay calculations
- Group booking discounts
- Early bird/last-minute pricing
- Occupancy-based pricing

### 2. **Business Logic**
- Minimum stay requirements
- Blackout dates
- Cancellation policies
- Tax calculations

### 3. **Performance**
- Price caching
- Bulk calculations
- Rate calendars
- Price history

## 🎯 Key Benefits

1. **Automated Pricing**: No manual rate management
2. **Dynamic Surcharges**: Automatic application of seasonal rates
3. **Consistent Logic**: Same pricing rules across all channels
4. **Real-time Calculation**: Instant price updates
5. **Audit Trail**: Clear breakdown of price components

## 📝 Integration Checklist

- [ ] PDF extraction working
- [ ] Structured data format validated
- [ ] Pricing engine function tested
- [ ] API endpoints functional
- [ ] Frontend component integrated
- [ ] Test scenarios passing
- [ ] Error handling implemented
- [ ] Documentation complete

The pricing engine completes your tariff extraction system, making it a fully functional solution for automated hotel pricing! 🎉
