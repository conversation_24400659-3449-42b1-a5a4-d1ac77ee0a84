#!/usr/bin/env python3
"""
Test script for the pricing engine using real extracted data.
This demonstrates how to use the calculate_final_price function.
"""

import sys
import os
from datetime import datetime, timedelta

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from extract_tariff import calculate_final_price

def test_pricing_engine():
    """Test the pricing engine with sample data from Upavan Resort."""
    
    print("🚀 Testing Pricing Engine with Upavan Resort Data")
    print("=" * 60)
    
    # Sample structured data from your successful extraction
    sample_data = {
        "base_tariffs": [
            {"room_category": "GREENVIEWDELUXENON-AC", "cp_price": 3500, "map_price": 4500, "ap_price": 5500},
            {"room_category": "HONEYMOONNESTNON-AC", "cp_price": 4500, "map_price": 5500, "ap_price": 6500},
            {"room_category": "HONEYMOONNESTAC", "cp_price": 5500, "map_price": 6500, "ap_price": 7500},
            {"room_category": "WOODSIDEDELUXENON-AC", "cp_price": 4500, "map_price": 5500, "ap_price": 6500},
            {"room_category": "WOODSIDEDELUXEAC", "cp_price": 5500, "map_price": 6500, "ap_price": 7500},
            {"room_category": "HILLVIEWPREMIUMAC", "cp_price": 6500, "map_price": 7500, "ap_price": 8500}
        ],
        "pricing_rules": [
            {
                "rule_name": "Seasonal Hike",
                "start_date": "2024-12-20",
                "end_date": "2025-01-15",
                "charge_type": "per_room",
                "amount": 25
            },
            {
                "rule_name": "New Year Gala Dinner Adult",
                "start_date": None,
                "end_date": None,
                "charge_type": "per_adult",
                "amount": 1500
            },
            {
                "rule_name": "New Year Gala Dinner Child",
                "start_date": None,
                "end_date": None,
                "charge_type": "per_child",
                "amount": 1200
            }
        ]
    }
    
    # Test scenarios
    test_cases = [
        {
            "name": "Regular Date - Green View Deluxe Non-AC (CP)",
            "date": "2025-03-15",
            "room": "GREENVIEW",
            "meal_plan": "cp",
            "expected_notes": "Base price only, no surcharges"
        },
        {
            "name": "Peak Season - Green View Deluxe Non-AC (CP)",
            "date": "2025-01-01",
            "room": "GREENVIEW",
            "meal_plan": "cp",
            "expected_notes": "Base price + seasonal hike"
        },
        {
            "name": "Regular Date - Hill View Premium AC (MAP)",
            "date": "2025-03-15",
            "room": "HILLVIEW",
            "meal_plan": "map",
            "expected_notes": "Premium room with MAP"
        },
        {
            "name": "Peak Season - Honeymoon Nest AC (AP)",
            "date": "2024-12-25",
            "room": "HONEYMOON",
            "meal_plan": "ap",
            "expected_notes": "Premium room during peak season"
        },
        {
            "name": "Invalid Room Test",
            "date": "2025-03-15",
            "room": "NONEXISTENT",
            "meal_plan": "cp",
            "expected_notes": "Should return None"
        }
    ]
    
    print(f"📊 Running {len(test_cases)} test scenarios...\n")
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"Test {i}: {test_case['name']}")
        print(f"   Date: {test_case['date']}")
        print(f"   Room: {test_case['room']}")
        print(f"   Meal Plan: {test_case['meal_plan'].upper()}")
        print(f"   Expected: {test_case['expected_notes']}")
        
        try:
            final_price = calculate_final_price(
                structured_data=sample_data,
                check_in_date_str=test_case['date'],
                room_category=test_case['room'],
                meal_plan=test_case['meal_plan']
            )
            
            if final_price is not None:
                formatted_price = f"₹{final_price:,}"
                print(f"   ✅ Result: {formatted_price}")
            else:
                print(f"   ❌ Result: No price found")
                
        except Exception as e:
            print(f"   ❌ Error: {e}")
        
        print()
    
    print("🎯 Pricing Engine Test Complete!")
    print("\n" + "=" * 60)
    print("💡 Usage Examples:")
    print("   • Use 'GREENVIEW' to match 'GREENVIEWDELUXENON-AC'")
    print("   • Use 'HONEYMOON' to match 'HONEYMOONNESTNON-AC' or 'HONEYMOONNESTAC'")
    print("   • Use 'HILLVIEW' to match 'HILLVIEWPREMIUMAC'")
    print("   • Meal plans: 'cp', 'map', 'ap'")
    print("   • Dates in 'YYYY-MM-DD' format")
    print("   • Peak season: 2024-12-20 to 2025-01-15 (+₹25 surcharge)")

def interactive_pricing_test():
    """Interactive test where user can input their own values."""
    print("\n🔧 Interactive Pricing Test")
    print("=" * 30)
    
    # Use the same sample data
    sample_data = {
        "base_tariffs": [
            {"room_category": "GREENVIEWDELUXENON-AC", "cp_price": 3500, "map_price": 4500, "ap_price": 5500},
            {"room_category": "HONEYMOONNESTNON-AC", "cp_price": 4500, "map_price": 5500, "ap_price": 6500},
            {"room_category": "HONEYMOONNESTAC", "cp_price": 5500, "map_price": 6500, "ap_price": 7500},
            {"room_category": "HILLVIEWPREMIUMAC", "cp_price": 6500, "map_price": 7500, "ap_price": 8500}
        ],
        "pricing_rules": [
            {"rule_name": "Seasonal Hike", "start_date": "2024-12-20", "end_date": "2025-01-15", "charge_type": "per_room", "amount": 25}
        ]
    }
    
    print("Available rooms: GREENVIEW, HONEYMOON, HILLVIEW")
    print("Available meal plans: cp, map, ap")
    print("Peak season: 2024-12-20 to 2025-01-15")
    print()
    
    try:
        room = input("Enter room category (e.g., GREENVIEW): ").strip()
        meal_plan = input("Enter meal plan (cp/map/ap): ").strip().lower()
        date = input("Enter check-in date (YYYY-MM-DD): ").strip()
        
        if room and meal_plan and date:
            final_price = calculate_final_price(sample_data, date, room, meal_plan)
            if final_price:
                print(f"\n✅ Final Price: ₹{final_price:,}")
            else:
                print("\n❌ Could not calculate price. Check your inputs.")
        else:
            print("\n❌ Please provide all required inputs.")
            
    except KeyboardInterrupt:
        print("\n\n👋 Test cancelled by user.")
    except Exception as e:
        print(f"\n❌ Error: {e}")

if __name__ == "__main__":
    test_pricing_engine()
    
    # Ask if user wants to try interactive test
    try:
        response = input("\nWould you like to try the interactive pricing test? (y/n): ").strip().lower()
        if response in ['y', 'yes']:
            interactive_pricing_test()
    except KeyboardInterrupt:
        print("\n\n👋 Goodbye!")
