#!/usr/bin/env python3
"""
Test script to verify the date parsing functionality
"""

import sys
import os

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from extract_tariff import parse_validity_period

def test_date_parsing():
    """Test the parse_validity_period function with the actual extracted data."""
    print("🧪 Testing Date Parsing Function")
    print("=" * 50)
    
    # Test cases based on actual OCR output
    test_cases = [
        {
            "name": "Actual OCR Output (with LaTeX)",
            "input": "from $01^{\\text {st }}$ June 2025 to $30^{\\text {th }}$ September 2025\nfrom $01^{\\text {st }}$ October 2025 to $31^{\\text {st }}$ May 2026",
            "expected_start": "2025-06-01",
            "expected_end": "2025-09-30"
        },
        {
            "name": "Simple Format",
            "input": "from 01 June 2025 to 30 September 2025",
            "expected_start": "2025-06-01", 
            "expected_end": "2025-09-30"
        },
        {
            "name": "Without 'from' keyword",
            "input": "01 June 2025 to 30 September 2025",
            "expected_start": "2025-06-01",
            "expected_end": "2025-09-30"
        },
        {
            "name": "Different Month Format",
            "input": "from 15 March 2025 to 31 December 2025",
            "expected_start": "2025-03-15",
            "expected_end": "2025-12-31"
        },
        {
            "name": "Empty Input",
            "input": "",
            "expected_start": None,
            "expected_end": None
        },
        {
            "name": "Invalid Format",
            "input": "some random text without dates",
            "expected_start": None,
            "expected_end": None
        },
        {
            "name": "Woodstock Format (with ordinals)",
            "input": "Rates are valid From April 01st 2025 to March 31st 2026.",
            "expected_start": "2025-04-01",
            "expected_end": "2026-03-31"
        },
        {
            "name": "Actual Upavan Format (from logs)",
            "input": "Special Rate from $01^{\\text {st }}$ June 2025 to $30^{\\text {th }}$ September 2025",
            "expected_start": "2025-06-01",
            "expected_end": "2025-09-30"
        },
        {
            "name": "Actual Woodstock Format (from logs)",
            "input": "Rates are valid From April 01 2025 to March 31 2026. Excluding Dussehra, Deepavali & From X-Mas to New Year.",
            "expected_start": "2025-04-01",
            "expected_end": "2026-03-31"
        }
    ]
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n📋 Test {i}: {test_case['name']}")
        print("-" * 40)
        print(f"Input: {test_case['input']}")
        
        try:
            start_date, end_date = parse_validity_period(test_case['input'])
            
            print(f"Output: start='{start_date}', end='{end_date}'")
            print(f"Expected: start='{test_case['expected_start']}', end='{test_case['expected_end']}'")
            
            # Check results
            if start_date == test_case['expected_start'] and end_date == test_case['expected_end']:
                print("✅ PASS")
            else:
                print("❌ FAIL")
                
        except Exception as e:
            print(f"❌ ERROR: {e}")
            import traceback
            traceback.print_exc()

def test_with_actual_ocr_data():
    """Test with the exact data from the OCR output."""
    print(f"\n\n🔍 Testing with Actual OCR Data")
    print("=" * 50)
    
    # This is the exact validity_period from the debug_triage_response.txt
    actual_validity_period = "from $01^{\\text {st }}$ June 2025 to $30^{\\text {th }}$ September 2025\nfrom $01^{\\text {st }}$ October 2025 to $31^{\\text {st }}$ May 2026"
    
    print(f"Raw input: {repr(actual_validity_period)}")
    
    start_date, end_date = parse_validity_period(actual_validity_period)
    
    print(f"Parsed result:")
    print(f"  Start Date: {start_date}")
    print(f"  End Date: {end_date}")
    
    if start_date and end_date:
        print("✅ Successfully parsed dates from OCR data!")
        print(f"📅 Date range: {start_date} to {end_date}")
    else:
        print("❌ Failed to parse dates from OCR data")
        
    return start_date, end_date

def main():
    """Main test function."""
    try:
        test_date_parsing()
        test_with_actual_ocr_data()
        
        print(f"\n\n🎯 Date Parsing Test Complete!")
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
