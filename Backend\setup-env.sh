#!/bin/bash

# Environment setup script for PDF Extraction Service
# This helps configure the .env file with actual API keys

echo "🔧 PDF Extraction Service - Environment Setup"
echo "=============================================="

# Check if .env file exists
if [ -f ".env" ]; then
    echo "📋 Current .env file found:"
    cat .env
    echo ""
    echo "Do you want to update it? (y/n)"
    read -r update_env
    if [ "$update_env" != "y" ]; then
        echo "Keeping existing .env file"
        exit 0
    fi
fi

echo "📝 Setting up environment variables..."

# Get Google API Key
echo "🔑 Enter your Google API Key (for Gemini 2.5 Pro):"
echo "   Get it from: https://makersuite.google.com/app/apikey"
read -r google_api_key

# Get Mistral API Key
echo "🔑 Enter your Mistral API Key (for OCR):"
echo "   Get it from: https://console.mistral.ai/"
read -r mistral_api_key

# Create .env file
cat > .env << EOF
# Google AI API Key (for Gemini 2.5 Pro)
GOOGLE_API_KEY=$google_api_key

# Mistral AI API Key (for OCR)
MISTRAL_API_KEY=$mistral_api_key

# Flask Configuration
FLASK_ENV=production
FLASK_DEBUG=False
EOF

echo "✅ Environment file created successfully!"
echo ""
echo "📋 Your .env file contains:"
cat .env

echo ""
echo "🔒 Security Note: Keep your API keys secure and never commit them to version control"
echo "🚀 You can now run the PDF extraction service"
