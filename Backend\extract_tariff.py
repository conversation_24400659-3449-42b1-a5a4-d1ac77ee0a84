import base64
import os
import requests
from mistralai import Mistral
from langchain_google_genai import ChatGoogleGenerativeAI
from langchain_core.pydantic_v1 import SecretStr
import json
import re
from datetime import datetime
from dotenv import load_dotenv

# Load environment variables
load_dotenv()
MISTRAL_API_KEY = os.getenv("MISTRAL_API_KEY")
GOOGLE_API_KEY = os.getenv("GOOGLE_API_KEY")

# --- Helper Functions ---

def safe_json_loads(json_string):
    """Safely loads a JSON string, stripping markdown and handling errors."""
    if not isinstance(json_string, str):
        print(f"❌ Error decoding JSON: Input is not a string, but {type(json_string)}")
        return None
    try:
        # Handle markdown code blocks
        if json_string.strip().startswith("```json"):
            json_string = json_string.strip()[7:-3].strip()
        elif json_string.strip().startswith("```"):
            json_string = json_string.strip()[3:-3].strip()

        # Additional cleanup for malformed JSON
        json_string = json_string.strip()

        # Fix common JSON issues
        # Remove trailing commas before closing brackets/braces
        json_string = re.sub(r',(\s*[}\]])', r'\1', json_string)

        # Fix invalid escape sequences like \& -> &
        json_string = re.sub(r'\\&', '&', json_string)
        json_string = re.sub(r'\\([^"\\\/bfnrt])', r'\1', json_string)

        return json.loads(json_string)
    except (json.JSONDecodeError, TypeError) as e:
        print(f"❌ Error decoding JSON: {e}\n   Raw string preview: {json_string[:500]}")

        # Try to extract JSON from the string if it's embedded in other text
        try:
            # Look for JSON object patterns
            json_match = re.search(r'\{.*\}', json_string, re.DOTALL)
            if json_match:
                extracted_json = json_match.group(0)
                # Apply the same fixes to extracted JSON
                extracted_json = re.sub(r',(\s*[}\]])', r'\1', extracted_json)
                extracted_json = re.sub(r'\\&', '&', extracted_json)
                extracted_json = re.sub(r'\\([^"\\\/bfnrt])', r'\1', extracted_json)
                return json.loads(extracted_json)
        except Exception as recovery_error:
            print(f"❌ JSON recovery also failed: {recovery_error}")
            pass

        return None

def parse_validity_period(period_string):
    """Parses a date range string into start and end dates."""
    if not period_string:
        return None, None

    # Handle case where period_string might be a list instead of a string
    if isinstance(period_string, list):
        # If it's a list, try to join it or take the first non-empty element
        if period_string:
            # Filter out empty strings and join with space
            valid_parts = [str(part).strip() for part in period_string if part and str(part).strip()]
            if valid_parts:
                period_string = " ".join(valid_parts)
            else:
                return None, None
        else:
            return None, None

    # Ensure it's a string
    if not isinstance(period_string, str):
        period_string = str(period_string)

    print(f"🔍 Debug: Original period string: {period_string}")

    # Clean up LaTeX formatting and other artifacts step by step
    # Remove LaTeX math mode markers ($...$)
    period_string = re.sub(r'\$([^$]*)\$', r'\1', period_string)

    # Remove complex LaTeX patterns like ^{\text {st }}
    period_string = re.sub(r'\^\\?\{\\?text\s*\\?\{([^}]*)\}\\?\}', r'\1', period_string)
    period_string = re.sub(r'\^\\?\{([^}]*)\}', r'\1', period_string)
    period_string = re.sub(r'\\text\s*\\?\{([^}]*)\}', r'\1', period_string)

    # Remove any remaining ^ markers and backslashes
    period_string = re.sub(r'\^', '', period_string)
    period_string = re.sub(r'\\', '', period_string)

    # Remove curly braces
    period_string = re.sub(r'[{}]', '', period_string)

    # Clean up ordinals (st, nd, rd, th)
    period_string = re.sub(r'(\d+)(st|nd|rd|th)', r'\1', period_string)

    # Clean up extra spaces and newlines
    period_string = re.sub(r'\s+', ' ', period_string).strip()

    print(f"🔍 Debug: Cleaned period string: {period_string}")

    # Comprehensive date pattern that handles both dates with and without years
    # This pattern matches various date formats
    date_pattern = r'(\d{1,2}\s+\w+\s+\d{4}|\w+\s+\d{1,2}(?:st|nd|rd|th)?\s+\d{4}|\d{4}-\d{1,2}-\d{1,2}|\d{1,2}\s+\w+|\w+\s+\d{1,2}(?:st|nd|rd|th)?)'

    # Try multiple date range patterns with case insensitive matching
    patterns_to_try = [
        # Standard "from X to Y" patterns
        r'from\s+' + date_pattern + r'\s+to\s+' + date_pattern,
        r'valid\s+from\s+' + date_pattern + r'\s+to\s+' + date_pattern,
        r'rates?\s+are\s+valid\s+from\s+' + date_pattern + r'\s+to\s+' + date_pattern,
        date_pattern + r'\s+to\s+' + date_pattern,

        # Parenthetical patterns like "(01 Oct – 31 March)"
        r'\(\s*' + date_pattern + r'\s*[–\-]\s*' + date_pattern + r'\s*\)',

        # Dash/hyphen patterns like "01 Oct – 31 March" or "01 Oct - 31 March"
        date_pattern + r'\s*[–\-]\s*' + date_pattern,

        # Season patterns like "Mid-Season (01 Oct – 31 March)"
        r'[^(]*\(\s*' + date_pattern + r'\s*[–\-]\s*' + date_pattern + r'\s*\)'
    ]

    for pattern_template in patterns_to_try:
        match = re.search(pattern_template, period_string, re.IGNORECASE)
        if match:
            print(f"🔍 Debug: Found date range with pattern: {match.group(0)}")

            # Extract all dates from the matched string
            all_dates = re.findall(date_pattern, match.group(0), re.IGNORECASE)

            if len(all_dates) >= 2:
                start_str = all_dates[0].strip()
                end_str = all_dates[1].strip()
                print(f"🔍 Debug: Extracted start: '{start_str}', end: '{end_str}'")
                break
            else:
                print(f"❌ Could not extract two dates from: {match.group(0)}")
                print(f"   Found dates: {all_dates}")
                continue
    else:
        print(f"❌ Could not find any date pattern in: {period_string}")
        return None, None

    # Helper function to add default year to dates without years
    def add_default_year(date_str):
        current_year = datetime.now().year
        # If the date string doesn't contain a 4-digit year, add current year
        if not re.search(r'\b\d{4}\b', date_str):
            return f"{date_str} {current_year}"
        return date_str

    # Add default year to dates if needed
    start_str_with_year = add_default_year(start_str)
    end_str_with_year = add_default_year(end_str)

    print(f"🔍 Debug: Start with year: '{start_str_with_year}', End with year: '{end_str_with_year}'")

    # Try multiple date formats (with and without years)
    date_formats = [
        '%d %B %Y',      # 01 October 2025
        '%d %b %Y',      # 01 Oct 2025
        '%B %d %Y',      # October 01 2025
        '%b %d %Y',      # Oct 01 2025
        '%d/%m/%Y',      # 01/10/2025
        '%d-%m-%Y',      # 01-10-2025
        '%Y-%m-%d',      # 2025-10-01
        '%d %B',         # 01 October (without year)
        '%d %b',         # 01 Oct (without year)
        '%B %d',         # October 01 (without year)
        '%b %d'          # Oct 01 (without year)
    ]

    # Try all combinations of formats for start and end dates
    # First try with years added
    for start_format in date_formats:
        for end_format in date_formats:
            try:
                start_parsed = datetime.strptime(start_str_with_year, start_format)
                end_parsed = datetime.strptime(end_str_with_year, end_format)

                # Handle year transitions (e.g., Oct 2025 to March 2026)
                if start_parsed > end_parsed:
                    # End date should be in the next year
                    end_parsed = end_parsed.replace(year=end_parsed.year + 1)
                    print(f"🔄 Adjusted end date to next year: {end_parsed}")

                start_date = start_parsed.strftime('%Y-%m-%d')
                end_date = end_parsed.strftime('%Y-%m-%d')
                print(f"✅ Successfully parsed dates: {start_date} to {end_date}")
                return start_date, end_date
            except ValueError:
                continue

    # If that fails, try original strings (in case they already have years)
    for start_format in date_formats:
        for end_format in date_formats:
            try:
                start_parsed = datetime.strptime(start_str, start_format)
                end_parsed = datetime.strptime(end_str, end_format)

                # Handle year transitions (e.g., Oct 2025 to March 2026)
                if start_parsed > end_parsed:
                    # End date should be in the next year
                    end_parsed = end_parsed.replace(year=end_parsed.year + 1)
                    print(f"🔄 Adjusted end date to next year: {end_parsed}")

                start_date = start_parsed.strftime('%Y-%m-%d')
                end_date = end_parsed.strftime('%Y-%m-%d')
                print(f"✅ Successfully parsed dates: {start_date} to {end_date}")
                return start_date, end_date
            except ValueError:
                continue

    print(f"❌ Could not parse validity period: {period_string}")
    print(f"   Start string: '{start_str}'")
    print(f"   End string: '{end_str}'")
    return None, None

def call_llm(client, prompt_text):
    """Generic function to call the LLM and return its content."""
    try:
        response = client.invoke(prompt_text)
        return response.content if hasattr(response, 'content') else str(response)
    except Exception as e:
        print(f"❌ A critical error occurred while calling the LLM: {e}")
        return None

# --- Prompt Templates ---

TRIAGE_PROMPT_TEMPLATE = """
You are a meticulous data analyst. Your task is to read the provided hotel tariff document text and segment it into distinct informational components.

Extract the following components and their raw text content:
- 'base_tariff_tables': Any tables showing room rates, prices, or tariffs
- 'seasonal_surcharges': Any seasonal pricing adjustments, peak season charges, or date-specific surcharges
- 'mandatory_add_ons': Any mandatory charges, taxes, or fees that must be added
- 'extra_person_policy': Policies and charges for extra adults or children
- 'validity_period': Date ranges when these tariffs are valid

IMPORTANT: Respond ONLY with a single, valid JSON object in this exact format:
{{
  "base_tariff_tables": "extracted text content here",
  "seasonal_surcharges": "extracted text content here",
  "mandatory_add_ons": "extracted text content here",
  "extra_person_policy": "extracted text content here",
  "validity_period": "extracted text content here"
}}

If a component is not found, use an empty string "" as the value. Do not include explanations, apologies, or any text outside of the JSON structure.

Document Text:
\"\"\"
{full_text}
\"\"\"
"""

BASE_TARIFF_PROMPT_TEMPLATE = """
You are a data extraction bot. Convert the provided hotel tariff table text into a clean JSON array of room rates.
For each room category, create a JSON object with: "room_category", "cp_price", "map_price", "ap_price".
Rules:
- All prices must be integers. Remove currency symbols and commas.
- If a price for a specific plan is not listed, its value must be 0.
- Exclude all rows for "Extra Person", "Extra Child", "Rack Rate", and "Published Rate".
IMPORTANT: Your entire response must be ONLY the JSON array.

Table Text:
\"\"\"
{table_text}
\"\"\"
"""

RULES_EXTRACTION_PROMPT_TEMPLATE = """
You are a rules engine specialist. Convert the provided text about hotel policies into a structured JSON array of pricing rules.

Each JSON object must include:
- "rule_name": Descriptive name of the rule
- "start_date": Start date in "YYYY-MM-DD" format (null if not specified)
- "end_date": End date in "YYYY-MM-DD" format (null if not specified)
- "charge_type": Must be 'per_room', 'per_adult', 'per_child', 'percentage', or 'fixed_amount'
- "amount": Numeric value (integer for fixed amounts, decimal for percentages like 0.20 for 20%)
- "applicable_room_categories": Array of room categories this rule applies to (empty array [] means all rooms)
- "applicable_meal_plans": Array of meal plans this rule applies to (empty array [] means all plans)
- "rule_type": Type of rule - 'surcharge', 'discount', 'seasonal_rate', 'extra_person', 'mandatory_fee'

Guidelines:
- Use "YYYY-MM-DD" format for dates. If a date is not present, use null.
- Assume the current year is 2025 unless specified otherwise.
- For percentage-based charges (like "20% extra"), use charge_type "percentage" and amount as decimal (0.20)
- For fixed amounts, use charge_type "per_room", "per_adult", or "per_child" as appropriate
- If a rule applies to specific room categories, list them in applicable_room_categories
- If a rule applies to specific meal plans, list them in applicable_meal_plans
- Empty arrays mean the rule applies to all categories/plans

IMPORTANT: Respond ONLY with the JSON array.

Policy Text:
\"\"\"
{rules_text}
\"\"\"
"""

# --- Core Logic ---

def extract_text_from_pdf(document_url, api_key):
    """Extract text from a PDF using a direct HTTP request to the Mistral OCR API."""
    print(f"🔍 OCR Debug: Starting extraction for: {document_url}")
    print(f"🔍 OCR Debug: API key present: {'Yes' if api_key else 'No'}")
    print(f"🔍 OCR Debug: API key starts with: {api_key[:10] if api_key else 'None'}...")

    if not document_url.startswith("http"):
        try:
            print(f"🔍 OCR Debug: Reading local file: {document_url}")
            with open(document_url, "rb") as f:
                pdf_content = f.read()
                print(f"🔍 OCR Debug: File size: {len(pdf_content)} bytes")
                base64_pdf = base64.b64encode(pdf_content).decode("utf-8")
                print(f"🔍 OCR Debug: Base64 encoded size: {len(base64_pdf)} characters")
            document_url = f"data:application/pdf;base64,{base64_pdf}"
            print(f"🔍 OCR Debug: Document URL length: {len(document_url)} characters")
        except Exception as e:
            print(f"❌ Error encoding local file to base64: {e}")
            import traceback
            traceback.print_exc()
            return ""

    print("🔍 OCR Debug: Calling Mistral OCR API via direct HTTP request...")
    url = "https://api.mistral.ai/v1/ocr"
    headers = {
        "Content-Type": "application/json",
        "Authorization": f"Bearer {api_key}"
    }
    data = {
        "model": "mistral-ocr-latest",
        "document": {"type": "document_url", "document_url": document_url},
        "include_image_base64": False
    }

    print(f"🔍 OCR Debug: Request URL: {url}")
    print(f"🔍 OCR Debug: Request headers: {headers}")
    print(f"🔍 OCR Debug: Request data keys: {list(data.keys())}")
    print(f"🔍 OCR Debug: Document type: {data['document']['type']}")

    try:
        print("🔍 OCR Debug: Sending request...")
        response = requests.post(url, headers=headers, json=data, timeout=120)

        print(f"🔍 OCR Debug: Response status: {response.status_code}")
        print(f"🔍 OCR Debug: Response headers: {dict(response.headers)}")

        if response.status_code == 200:
            ocr_response = response.json()
            print("✅ OCR Response received successfully")
            print(f"🔍 OCR Debug: Response keys: {list(ocr_response.keys())}")

            # Save full response for debugging
            os.makedirs('output', exist_ok=True)
            with open('output/debug_ocr_full_response.json', 'w', encoding='utf-8') as f:
                json.dump(ocr_response, f, indent=2, ensure_ascii=False)
            print("🔍 OCR Debug: Full response saved to output/debug_ocr_full_response.json")

            # Extract text from the pages' markdown content
            if 'pages' in ocr_response and isinstance(ocr_response['pages'], list):
                pages = ocr_response['pages']
                print(f"🔍 OCR Debug: Found {len(pages)} pages")

                full_text = ""
                for i, page in enumerate(pages):
                    page_text = page.get('markdown', '')
                    print(f"🔍 OCR Debug: Page {i+1} text length: {len(page_text)} characters")
                    if page_text:
                        full_text += page_text + "\n"

                print(f"✅ Extracted {len(full_text)} characters from OCR")
                if full_text.strip():
                    return full_text
                else:
                    print("❌ OCR extracted empty text")
                    return ""
            else:
                print("❌ Unable to extract text from OCR response - no pages found")
                print(f"🔍 OCR Debug: Response structure: {ocr_response}")
                return ""
        else:
            print(f"❌ OCR API request failed with status {response.status_code}")
            print(f"❌ Response text: {response.text}")

            # Try to parse error response
            try:
                error_data = response.json()
                print(f"❌ Error details: {error_data}")
            except:
                print("❌ Could not parse error response as JSON")

            return ""

    except requests.exceptions.RequestException as e:
        print(f"❌ An error occurred with the OCR HTTP request: {e}")
        import traceback
        traceback.print_exc()
        return ""
    except Exception as e:
        print(f"❌ Error processing OCR for {document_url}: {e}")
        import traceback
        traceback.print_exc()
        return ""

def orchestrate_tariff_extraction(full_text, llm_client):
    """Orchestrates the multi-step LLM extraction process."""
    print("🤖 Step 1: Triage and Component Extraction...")
    triage_prompt = TRIAGE_PROMPT_TEMPLATE.format(full_text=full_text)
    components_json_str = call_llm(llm_client, triage_prompt)

    # Debug: Save the raw LLM response
    os.makedirs('output', exist_ok=True)
    with open('output/debug_triage_response.txt', 'w', encoding='utf-8') as f:
        f.write(str(components_json_str))
    print(f"🔍 Debug: Raw triage response saved to output/debug_triage_response.txt")
    print(f"🔍 Debug: Raw triage response type: {type(components_json_str)}")
    print(f"🔍 Debug: Raw triage response preview: {str(components_json_str)[:200]}...")

    components = safe_json_loads(components_json_str)

    # Debug: Save the parsed components
    with open('output/debug_triage_parsed.json', 'w', encoding='utf-8') as f:
        json.dump(components, f, indent=2, ensure_ascii=False)
    print(f"🔍 Debug: Parsed triage components saved to output/debug_triage_parsed.json")

    if not components or not isinstance(components, dict):
        print("❌ CRITICAL: Triage step failed. Could not get valid JSON components from LLM.")
        print(f"🔍 Debug: Components type: {type(components)}")
        print(f"🔍 Debug: Components value: {components}")
        return {"error": "Triage step failed to produce valid components."}

    print(f"✅ Triage successful. Found components: {list(components.keys())}")
    final_data = {"base_tariffs": [], "pricing_rules": []}

    # --- Parse the validity period from PDF ---
    validity_period = components.get('validity_period')
    start_date, end_date = parse_validity_period(validity_period)
    if start_date and end_date:
        print(f"✅ Parsed validity period: {start_date} to {end_date}")
    else:
        print(f"⚠️ Could not parse validity period from PDF: {validity_period}")
        print("   -> Tariff records will not include date ranges (to be set manually)")
        # Don't set fallback dates - let frontend handle missing dates
        start_date, end_date = None, None

    # Step 2a: Extract Base Tariffs
    base_tariff_tables = components.get('base_tariff_tables', [])
    if base_tariff_tables:
        print("🤖 Step 2a: Extracting Base Tariffs...")

        # Ensure base_tariff_tables is a list of strings
        if isinstance(base_tariff_tables, str):
            table_text = base_tariff_tables
        elif isinstance(base_tariff_tables, list):
            # Filter out empty strings and join
            table_parts = [str(part).strip() for part in base_tariff_tables if part and str(part).strip()]
            table_text = "\n".join(table_parts)
        else:
            table_text = str(base_tariff_tables)

        print(f"🔍 Debug: Base tariff table text length: {len(table_text)} characters")

        tariff_prompt = BASE_TARIFF_PROMPT_TEMPLATE.format(table_text=table_text)
        tariffs_json_str = call_llm(llm_client, tariff_prompt)
        base_tariffs = safe_json_loads(tariffs_json_str) or []

        # --- Add the date range to each tariff record only if parsed from PDF ---
        for tariff in base_tariffs:
            # Only add dates if they were successfully parsed from the PDF
            if start_date and end_date:
                tariff['start_date'] = start_date
                tariff['end_date'] = end_date
            else:
                # Don't add date fields if not found in PDF - let frontend handle this
                pass

        final_data["base_tariffs"] = base_tariffs
        print(f"   -> Extracted {len(final_data['base_tariffs'])} base tariff records.")
    else:
        print("   -> No base tariff tables found to extract.")
        final_data["base_tariffs"] = []

    # Step 2b: Extract Pricing Rules
    print("🤖 Step 2b: Extracting Pricing Rules...")

    # Safely extract and flatten all rule components
    seasonal_surcharges = components.get('seasonal_surcharges', [])
    mandatory_add_ons = components.get('mandatory_add_ons', [])
    extra_person_policy = components.get('extra_person_policy', [])

    # Ensure all components are lists and flatten any nested structures
    def flatten_to_strings(item):
        if isinstance(item, str):
            return [item] if item.strip() else []
        elif isinstance(item, list):
            result = []
            for subitem in item:
                result.extend(flatten_to_strings(subitem))
            return result
        else:
            return [str(item)] if item else []

    # Flatten and combine all rule text parts
    all_rules_text = []
    all_rules_text.extend(flatten_to_strings(seasonal_surcharges))
    all_rules_text.extend(flatten_to_strings(mandatory_add_ons))
    all_rules_text.extend(flatten_to_strings(extra_person_policy))

    # Filter out empty strings
    rules_text_parts = [text.strip() for text in all_rules_text if text and text.strip()]

    print(f"🔍 Debug: Found {len(rules_text_parts)} rule text parts")

    if rules_text_parts:
        rules_text = "\n".join(rules_text_parts)
        print(f"🔍 Debug: Combined rules text length: {len(rules_text)} characters")

        rules_prompt = RULES_EXTRACTION_PROMPT_TEMPLATE.format(rules_text=rules_text)
        rules_json_str = call_llm(llm_client, rules_prompt)
        final_data["pricing_rules"] = safe_json_loads(rules_json_str) or []
        print(f"   -> Extracted {len(final_data['pricing_rules'])} pricing rules.")
    else:
        print("   -> No pricing rules text found to extract.")
        final_data["pricing_rules"] = []

    return final_data

def extract_tariff_from_pdf(pdf_path):
    """Main wrapper function to run the complete PDF extraction process."""
    print(f"\n--- Starting Full Extraction for: {pdf_path} ---")

    if not MISTRAL_API_KEY or not GOOGLE_API_KEY:
        return {"error": "API keys are not configured on the server."}

    # 1. OCR Step
    print("🔍 Step 1: OCR Text Extraction...")
    full_text = extract_text_from_pdf(pdf_path, MISTRAL_API_KEY)
    if not full_text:
        return {"error": "OCR failed to extract any text from the PDF."}
    
    # Save OCR output for debugging
    os.makedirs('output', exist_ok=True)
    with open('output/debug_ocr_output.txt', 'w', encoding='utf-8') as f:
        f.write(full_text)
    print("   -> OCR output saved to output/debug_ocr_output.txt")

    # 2. LLM Orchestration Step
    print("🤖 Step 2: LLM Orchestration...")
    try:
        llm_client = ChatGoogleGenerativeAI(
            model="gemini-2.5-pro",  # Using a fast and capable model
            api_key=SecretStr(GOOGLE_API_KEY)
        )
        print("   -> LLM client created successfully with gemini-2.5-pro.")
        
        structured_data = orchestrate_tariff_extraction(full_text, llm_client)
        print("--- Extraction Complete ---")
        return structured_data

    except Exception as e:
        print(f"❌ A critical error occurred in the LLM step: {e}")
        return {"error": f"LLM orchestration failed: {str(e)}"}

# --- Pricing Engine ---

def calculate_final_price(structured_data, check_in_date_str, room_category, meal_plan, num_adults=2, num_children=0):
    """
    Enhanced pricing calculator that integrates room categories, date ranges, and pricing rules.

    Args:
        structured_data (dict): The output from orchestrate_tariff_extraction.
        check_in_date_str (str): The date to check, in "YYYY-MM-DD" format.
        room_category (str): The desired room category.
        meal_plan (str): The desired meal plan ('cp', 'map', 'ap').
        num_adults (int): Number of adults (default: 2).
        num_children (int): Number of children (default: 0).

    Returns:
        dict: Detailed pricing breakdown with final price, or None if not found.
    """
    if not structured_data or not structured_data.get('base_tariffs'):
        print("❌ Pricing Engine: Cannot calculate price, base_tariffs are missing.")
        return None

    # 1. Find the base price for the selected room and meal plan
    base_price = 0
    meal_plan_key = f"{meal_plan.lower()}_price"
    matched_tariff = None

    for tariff in structured_data['base_tariffs']:
        # Improved matching with fuzzy logic
        if (room_category.lower() in tariff.get('room_category', '').lower() or
            tariff.get('room_category', '').lower() in room_category.lower()):
            if meal_plan_key in tariff and tariff[meal_plan_key] > 0:
                base_price = tariff[meal_plan_key]
                matched_tariff = tariff
                break

    if base_price == 0:
        print(f"❌ Pricing Engine: No base price found for {room_category} with plan {meal_plan}")
        return None

    print(f"✅ Pricing Engine: Found base price for {room_category}: {base_price}")

    # Initialize pricing breakdown
    pricing_breakdown = {
        'base_price': base_price,
        'room_category': room_category,
        'meal_plan': meal_plan,
        'check_in_date': check_in_date_str,
        'num_adults': num_adults,
        'num_children': num_children,
        'applied_rules': [],
        'surcharges': 0,
        'discounts': 0,
        'extra_person_charges': 0,
        'mandatory_fees': 0,
        'final_price': base_price
    }

    check_in_date = datetime.strptime(check_in_date_str, "%Y-%m-%d").date()

    # 2. Apply pricing rules in correct order: Room-based rules first, then person-based, then fees

    # Step 2a: Apply room-based rules (surcharges, discounts, seasonal rates)
    room_total = base_price  # Track room total for percentage calculations

    for rule in structured_data.get('pricing_rules', []):
        if not _is_rule_applicable(rule, check_in_date, room_category, meal_plan):
            continue

        rule_type = rule.get('rule_type', 'surcharge')

        # Only process room-based rules in this step
        if rule_type not in ['surcharge', 'seasonal_rate', 'discount']:
            continue

        rule_amount = _calculate_rule_amount(rule, base_price, num_adults, num_children)
        if rule_amount == 0:
            continue

        applied_rule = {
            'rule_name': rule.get('rule_name', 'Unknown Rule'),
            'rule_type': rule_type,
            'charge_type': rule.get('charge_type', 'per_room'),
            'amount': rule_amount,
            'description': _get_rule_description(rule, rule_amount)
        }

        if rule_type in ['surcharge', 'seasonal_rate']:
            pricing_breakdown['surcharges'] += rule_amount
            room_total += rule_amount  # Update room total for subsequent calculations
        elif rule_type == 'discount':
            pricing_breakdown['discounts'] += abs(rule_amount)
            room_total -= abs(rule_amount)  # Update room total for subsequent calculations

        pricing_breakdown['applied_rules'].append(applied_rule)
        print(f"   -> Applied {rule_type}: '{rule.get('rule_name')}' = {rule_amount:+}")

    # Step 2b: Apply extra person charges (NOT affected by seasonal surcharges)
    for rule in structured_data.get('pricing_rules', []):
        if not _is_rule_applicable(rule, check_in_date, room_category, meal_plan):
            continue

        rule_type = rule.get('rule_type', 'surcharge')

        # Only process extra person rules in this step
        if rule_type != 'extra_person':
            continue

        rule_amount = _calculate_rule_amount(rule, base_price, num_adults, num_children)
        if rule_amount == 0:
            continue

        applied_rule = {
            'rule_name': rule.get('rule_name', 'Unknown Rule'),
            'rule_type': rule_type,
            'charge_type': rule.get('charge_type', 'per_room'),
            'amount': rule_amount,
            'description': _get_rule_description(rule, rule_amount)
        }

        pricing_breakdown['extra_person_charges'] += rule_amount
        pricing_breakdown['applied_rules'].append(applied_rule)
        print(f"   -> Applied {rule_type}: '{rule.get('rule_name')}' = {rule_amount:+}")

    # Step 2c: Apply mandatory fees (taxes, service charges) - calculated on room + extra person total
    subtotal_before_fees = room_total + pricing_breakdown['extra_person_charges']

    for rule in structured_data.get('pricing_rules', []):
        if not _is_rule_applicable(rule, check_in_date, room_category, meal_plan):
            continue

        rule_type = rule.get('rule_type', 'surcharge')

        # Only process mandatory fee rules in this step
        if rule_type != 'mandatory_fee':
            continue

        # For percentage-based mandatory fees (like taxes), calculate on subtotal
        if rule.get('charge_type') == 'percentage':
            rule_amount = int(subtotal_before_fees * rule.get('amount', 0))
        else:
            rule_amount = _calculate_rule_amount(rule, base_price, num_adults, num_children)

        if rule_amount == 0:
            continue

        applied_rule = {
            'rule_name': rule.get('rule_name', 'Unknown Rule'),
            'rule_type': rule_type,
            'charge_type': rule.get('charge_type', 'per_room'),
            'amount': rule_amount,
            'description': _get_rule_description(rule, rule_amount, subtotal_before_fees)
        }

        pricing_breakdown['mandatory_fees'] += rule_amount
        pricing_breakdown['applied_rules'].append(applied_rule)
        print(f"   -> Applied {rule_type}: '{rule.get('rule_name')}' = {rule_amount:+}")

    # 3. Calculate final price
    pricing_breakdown['final_price'] = (
        pricing_breakdown['base_price'] +
        pricing_breakdown['surcharges'] -
        pricing_breakdown['discounts'] +
        pricing_breakdown['extra_person_charges'] +
        pricing_breakdown['mandatory_fees']
    )

    print(f"✅ Pricing Engine: Final calculated price for {check_in_date_str}: {pricing_breakdown['final_price']}")
    return pricing_breakdown

def _is_rule_applicable(rule, check_in_date, room_category, meal_plan):
    """Check if a pricing rule applies to the given parameters."""

    # Check date applicability
    date_applicable = True
    if rule.get('start_date') and rule.get('end_date'):
        try:
            start = datetime.strptime(rule['start_date'], "%Y-%m-%d").date()
            end = datetime.strptime(rule['end_date'], "%Y-%m-%d").date()
            date_applicable = start <= check_in_date <= end
        except (ValueError, TypeError):
            date_applicable = False
    elif rule.get('start_date') and not rule.get('end_date'):
        try:
            start = datetime.strptime(rule['start_date'], "%Y-%m-%d").date()
            date_applicable = check_in_date == start
        except (ValueError, TypeError):
            date_applicable = False

    if not date_applicable:
        return False

    # Check room category applicability
    applicable_rooms = rule.get('applicable_room_categories', [])
    if applicable_rooms:  # If specific rooms are listed
        room_applicable = any(
            room.lower() in room_category.lower() or room_category.lower() in room.lower()
            for room in applicable_rooms
        )
        if not room_applicable:
            return False

    # Check meal plan applicability
    applicable_plans = rule.get('applicable_meal_plans', [])
    if applicable_plans:  # If specific plans are listed
        plan_applicable = any(
            plan.lower() == meal_plan.lower()
            for plan in applicable_plans
        )
        if not plan_applicable:
            return False

    return True

def _calculate_rule_amount(rule, base_price, num_adults, num_children):
    """Calculate the monetary amount for a pricing rule."""
    charge_type = rule.get('charge_type', 'per_room')
    amount = rule.get('amount', 0)

    if charge_type == 'percentage':
        # For percentage-based charges (e.g., 20% surcharge)
        return int(base_price * amount)
    elif charge_type == 'per_room':
        return amount
    elif charge_type == 'per_adult':
        return amount * num_adults
    elif charge_type == 'per_child':
        return amount * num_children
    elif charge_type == 'fixed_amount':
        return amount
    else:
        # Default to per_room
        return amount

def _get_rule_description(rule, calculated_amount, base_amount=None):
    """Generate a human-readable description for a pricing rule."""
    rule_name = rule.get('rule_name', 'Unknown Rule')
    charge_type = rule.get('charge_type', 'per_room')
    original_amount = rule.get('amount', 0)
    rule_type = rule.get('rule_type', 'surcharge')

    if charge_type == 'percentage':
        percentage = int(original_amount * 100)
        if rule_type == 'mandatory_fee' and base_amount:
            return f"{rule_name}: {percentage}% on ₹{base_amount:,} (₹{calculated_amount:,})"
        else:
            return f"{rule_name}: {percentage}% surcharge (₹{calculated_amount:,})"
    elif charge_type == 'per_adult':
        return f"{rule_name}: ₹{original_amount:,} per adult (₹{calculated_amount:,} total)"
    elif charge_type == 'per_child':
        return f"{rule_name}: ₹{original_amount:,} per child (₹{calculated_amount:,} total)"
    else:
        return f"{rule_name}: ₹{calculated_amount:,}"

if __name__ == "__main__":
    print("This module is designed to be imported and used by other scripts.")
    print("Use extract_tariff_from_pdf(pdf_path) to extract tariff data from a PDF.")
    print("Use calculate_final_price(structured_data, date, room, meal_plan, adults, children) to calculate prices.")