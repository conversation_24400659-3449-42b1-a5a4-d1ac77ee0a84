#!/bin/bash

# Auto-deployment script to run directly on the Linode server
# This script pulls the latest code from hotel-tariff branch and deploys it

echo "🚀 Starting Auto-Deployment for Backend PDF Extraction"
echo "======================================================"

# Configuration
REPO_URL="https://github.com/tripmilestone/Tripmilestone-admin-frontend.git"
BRANCH="hotel-tariff"
DEPLOY_DIR="/root/tariff-extraction"
APP_NAME="tariff-extraction"
BACKUP_DIR="/root/tariff-extraction-backup-$(date +%Y%m%d-%H%M%S)"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if running as root
if [ "$EUID" -ne 0 ]; then
    print_error "Please run this script as root"
    exit 1
fi

print_status "Starting deployment process..."

# Navigate to root directory
cd /root || exit 1

# Backup existing deployment if it exists
if [ -d "$DEPLOY_DIR" ]; then
    print_status "Creating backup of existing deployment..."
    if cp -r "$DEPLOY_DIR" "$BACKUP_DIR"; then
        print_success "Backup created: $BACKUP_DIR"
    else
        print_warning "Failed to create backup, continuing anyway..."
    fi
    
    # Stop the existing application
    if command -v pm2 &> /dev/null; then
        print_status "Stopping existing PM2 process..."
        pm2 stop "$APP_NAME" 2>/dev/null || true
        pm2 delete "$APP_NAME" 2>/dev/null || true
    fi
    
    # Remove existing deployment
    print_status "Removing existing deployment..."
    rm -rf "$DEPLOY_DIR"
fi

# Clone the repository
print_status "Cloning repository (branch: $BRANCH)..."
if git clone -b "$BRANCH" "$REPO_URL" tariff-extraction; then
    print_success "Repository cloned successfully"
else
    print_error "Failed to clone repository"
    
    # Restore backup if clone failed
    if [ -d "$BACKUP_DIR" ]; then
        print_status "Restoring backup due to clone failure..."
        mv "$BACKUP_DIR" "$DEPLOY_DIR"
        print_warning "Restored previous deployment"
    fi
    exit 1
fi

# Navigate to Backend directory
cd "$DEPLOY_DIR/Backend" || exit 1

# Detect Python command
if command -v python3 &> /dev/null; then
    PYTHON_CMD="python3"
elif command -v python &> /dev/null; then
    PYTHON_CMD="python"
else
    print_error "Python not found!"
    exit 1
fi

print_status "Using Python: $PYTHON_CMD"

# Install dependencies
print_status "Installing Python dependencies..."
if [ -f "requirements.txt" ]; then
    $PYTHON_CMD -m pip install --upgrade pip
    if $PYTHON_CMD -m pip install -r requirements.txt; then
        print_success "Dependencies installed successfully"
    else
        print_error "Failed to install dependencies"
        exit 1
    fi
else
    print_warning "No requirements.txt found, installing essential packages..."
    $PYTHON_CMD -m pip install flask flask-cors requests python-dotenv langchain-google-genai pydantic
fi

# Check environment file
if [ -f ".env" ]; then
    print_success "Environment file found"
else
    print_warning "No .env file found in repository"
    
    # Check if .env exists in backup
    if [ -f "$BACKUP_DIR/Backend/.env" ]; then
        print_status "Copying .env from backup..."
        cp "$BACKUP_DIR/Backend/.env" .
        print_success "Environment file restored from backup"
    else
        print_warning "Creating basic .env template..."
        cat > .env << 'EOF'
# Google AI API Key (for Gemini 2.5 Pro)
GOOGLE_API_KEY=your_google_api_key_here

# Mistral AI API Key (for OCR)
MISTRAL_API_KEY=your_mistral_api_key_here

# Flask Configuration
FLASK_ENV=production
FLASK_DEBUG=False
EOF
        print_warning "Please update .env file with your actual API keys"
    fi
fi

# Set proper permissions
chmod +x deploy.sh 2>/dev/null || true

# Manage with PM2 if available
if command -v pm2 &> /dev/null; then
    print_status "Starting application with PM2..."
    
    # Start new process
    if pm2 start app.py --name "$APP_NAME" --interpreter "$PYTHON_CMD"; then
        pm2 save
        print_success "Application started with PM2"
        
        # Show status
        print_status "PM2 Status:"
        pm2 status
        
        # Show recent logs
        print_status "Recent logs:"
        pm2 logs "$APP_NAME" --lines 10
        
    else
        print_error "Failed to start application with PM2"
        exit 1
    fi
    
else
    print_warning "PM2 not found. Starting application in background..."
    nohup $PYTHON_CMD app.py > /tmp/tariff-extraction.log 2>&1 &
    print_success "Application started in background"
    print_status "Logs available at: /tmp/tariff-extraction.log"
fi

# Test the application
print_status "Testing application..."
sleep 5

if curl -f http://localhost:5000/ 2>/dev/null; then
    print_success "✅ Application is responding!"
else
    print_warning "⚠️ Application may still be starting up"
    print_status "Check logs with: pm2 logs $APP_NAME"
fi

# Cleanup old backups (keep only last 3)
print_status "Cleaning up old backups..."
cd /root
ls -dt tariff-extraction-backup-* 2>/dev/null | tail -n +4 | xargs rm -rf 2>/dev/null || true

# Final status
print_success "🎉 Deployment completed successfully!"
echo ""
print_status "📍 Deployment Details:"
echo "  - Location: $DEPLOY_DIR"
echo "  - Backend: $DEPLOY_DIR/Backend"
echo "  - Branch: $BRANCH"
echo "  - Python: $PYTHON_CMD"
echo "  - Backup: $BACKUP_DIR"
echo ""
print_status "🔧 Management Commands:"
echo "  - Check status: pm2 status"
echo "  - View logs: pm2 logs $APP_NAME"
echo "  - Restart: pm2 restart $APP_NAME"
echo "  - Stop: pm2 stop $APP_NAME"
echo ""
print_status "🌐 Application should be running on: http://localhost:5000"

# Show git commit info
cd "$DEPLOY_DIR"
print_status "📋 Deployed Commit:"
git log --oneline -1

echo ""
print_success "Deployment completed! 🚀"
