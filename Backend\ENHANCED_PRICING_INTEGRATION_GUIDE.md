# Enhanced Pricing Rules & Surcharges Integration Guide

## Overview

This guide explains how to integrate pricing rules and surcharges with specific room categories, separate date ranges, and pricing calculations in the Tripmilestone admin system.

## Key Features

### 1. **Room Category-Specific Rules**
- Apply different rules to different room types
- Support for partial matching (e.g., "Deluxe" matches "Deluxe Room")
- Empty array `[]` means rule applies to all room categories

### 2. **Date Range-Based Pricing**
- Seasonal rates with specific start/end dates
- Single-day events (start date only)
- Always-applicable rules (no date restrictions)

### 3. **Meal Plan Integration**
- Rules can target specific meal plans (CP, MAP, AP)
- Empty array `[]` means rule applies to all meal plans

### 4. **Multiple Rule Types**
- **Surcharges**: Additional charges (weekend, peak season)
- **Discounts**: Price reductions (promotional offers)
- **Seasonal Rates**: Date-specific pricing adjustments
- **Extra Person**: Charges for additional guests
- **Mandatory Fees**: Required charges (taxes, service fees)

### 5. **Flexible Charge Types**
- **Percentage**: Based on base price (e.g., 20% surcharge)
- **Per Room**: Fixed amount per room
- **Per Adult**: Amount multiplied by number of adults
- **Per Child**: Amount multiplied by number of children
- **Fixed Amount**: Absolute amount regardless of occupancy

## Data Structure

### Enhanced Pricing Rule Format

```json
{
  "rule_name": "Christmas Peak Season",
  "start_date": "2025-12-24",
  "end_date": "2026-01-02",
  "charge_type": "percentage",
  "amount": 0.30,
  "applicable_room_categories": ["Premium Villa", "Suite Room"],
  "applicable_meal_plans": ["ap", "map"],
  "rule_type": "seasonal_rate"
}
```

### Field Descriptions

| Field | Type | Description | Example |
|-------|------|-------------|---------|
| `rule_name` | String | Descriptive name | "Weekend Surcharge" |
| `start_date` | String/null | Start date (YYYY-MM-DD) | "2025-07-26" |
| `end_date` | String/null | End date (YYYY-MM-DD) | "2025-07-27" |
| `charge_type` | String | How to calculate amount | "percentage", "per_room" |
| `amount` | Number | Value for calculation | 0.15 (15%), 1500 (₹1500) |
| `applicable_room_categories` | Array | Target room types | ["Deluxe Room"] or [] |
| `applicable_meal_plans` | Array | Target meal plans | ["ap"] or [] |
| `rule_type` | String | Category of rule | "surcharge", "discount" |

## Usage Examples

### 1. Weekend Surcharge (Room-Specific)

```json
{
  "rule_name": "Weekend Surcharge",
  "start_date": "2025-07-26",
  "end_date": "2025-07-27",
  "charge_type": "percentage",
  "amount": 0.15,
  "applicable_room_categories": ["Deluxe Room", "Suite Room"],
  "applicable_meal_plans": [],
  "rule_type": "surcharge"
}
```

**Result**: 15% surcharge on Deluxe and Suite rooms during weekend

### 2. Peak Season Rate (All Rooms)

```json
{
  "rule_name": "Christmas Peak Season",
  "start_date": "2025-12-24",
  "end_date": "2026-01-02",
  "charge_type": "percentage",
  "amount": 0.30,
  "applicable_room_categories": [],
  "applicable_meal_plans": [],
  "rule_type": "seasonal_rate"
}
```

**Result**: 30% surcharge on all rooms during Christmas period

### 3. Meal Plan-Specific Discount

```json
{
  "rule_name": "AP Plan Discount",
  "start_date": "2025-08-01",
  "end_date": "2025-08-15",
  "charge_type": "fixed_amount",
  "amount": 2000,
  "applicable_room_categories": ["Premium Villa"],
  "applicable_meal_plans": ["ap"],
  "rule_type": "discount"
}
```

**Result**: ₹2000 discount on Premium Villa with AP plan in August

### 4. Extra Person Charges

```json
{
  "rule_name": "Extra Adult Charge",
  "start_date": null,
  "end_date": null,
  "charge_type": "per_adult",
  "amount": 1500,
  "applicable_room_categories": [],
  "applicable_meal_plans": [],
  "rule_type": "extra_person"
}
```

**Result**: ₹1500 per additional adult, always applicable

## API Integration

### Calculate Pricing Endpoint

```python
@app.route('/api/calculate-price', methods=['POST'])
def calculate_room_price():
    data = request.get_json()
    
    pricing_result = calculate_final_price(
        data['tariff_data'],
        data['check_in_date'],
        data['room_category'],
        data['meal_plan'],
        data.get('adults', 2),
        data.get('children', 0)
    )
    
    return jsonify({
        'success': True,
        'pricing': {
            'base_price': pricing_result['base_price'],
            'surcharges': pricing_result['surcharges'],
            'discounts': pricing_result['discounts'],
            'final_price': pricing_result['final_price']
        },
        'applied_rules': pricing_result['applied_rules']
    })
```

### Sample API Response

```json
{
  "success": true,
  "booking_details": {
    "check_in_date": "2025-12-25",
    "room_category": "Premium Villa",
    "meal_plan": "AP",
    "guests": {"adults": 2, "children": 1}
  },
  "pricing": {
    "base_price": 16000,
    "surcharges": 4800,
    "discounts": 0,
    "extra_person_charges": 800,
    "mandatory_fees": 3380,
    "final_price": 24980,
    "currency": "INR"
  },
  "applied_rules": [
    {
      "name": "Christmas Peak Season",
      "type": "seasonal_rate",
      "amount": 4800,
      "description": "Christmas Peak Season: 30% surcharge (₹4800)"
    },
    {
      "name": "Extra Child Charge",
      "type": "extra_person",
      "amount": 800,
      "description": "Extra Child Charge: ₹800 per child (₹800 total)"
    }
  ]
}
```

## Implementation Benefits

### 1. **Flexible Rule Management**
- Easy to add new rule types
- Room and meal plan targeting
- Date-based activation/deactivation

### 2. **Transparent Pricing**
- Detailed breakdown for customers
- Clear rule descriptions
- Separate categorization of charges

### 3. **Business Logic Support**
- Seasonal pricing strategies
- Promotional campaigns
- Dynamic surcharge management

### 4. **Scalable Architecture**
- Supports complex pricing scenarios
- Easy integration with booking systems
- Comprehensive API responses

## Testing

Run the demo scripts to see the integration in action:

```bash
# Test the enhanced pricing engine
python enhanced_pricing_demo.py

# Test API integration
python api_integration_example.py
```

## Next Steps

1. **Frontend Integration**: Connect the pricing API to your booking interface
2. **Rule Management UI**: Create admin interface for managing pricing rules
3. **Reporting**: Add analytics for rule effectiveness
4. **Advanced Features**: Implement group discounts, loyalty programs, etc.

This enhanced pricing system provides a robust foundation for complex hotel pricing scenarios while maintaining flexibility and transparency.
