#!/usr/bin/env python3
"""
API Integration Example for Enhanced Pricing Engine
Shows how to integrate the enhanced pricing rules with the Flask API
"""

import json
import requests
from datetime import datetime, timedelta

def test_pricing_api_integration():
    """Test the enhanced pricing integration with the Flask API."""
    print("🚀 API Integration Test for Enhanced Pricing")
    print("=" * 60)
    
    # Sample extracted tariff data (this would come from PDF extraction)
    sample_extracted_data = {
        "base_tariffs": [
            {
                "room_category": "Deluxe Room",
                "cp_price": 5000,
                "map_price": 6000,
                "ap_price": 7000,
                "start_date": "2025-01-01",
                "end_date": "2025-12-31"
            },
            {
                "room_category": "Suite Room",
                "cp_price": 8000,
                "map_price": 9000,
                "ap_price": 10000,
                "start_date": "2025-01-01",
                "end_date": "2025-12-31"
            }
        ],
        "pricing_rules": [
            {
                "rule_name": "Christmas Peak Season",
                "start_date": "2025-12-24",
                "end_date": "2026-01-02",
                "charge_type": "percentage",
                "amount": 0.25,
                "applicable_room_categories": [],
                "applicable_meal_plans": [],
                "rule_type": "seasonal_rate"
            },
            {
                "rule_name": "Weekend Surcharge",
                "start_date": "2025-07-26",
                "end_date": "2025-07-27",
                "charge_type": "percentage",
                "amount": 0.15,
                "applicable_room_categories": ["Deluxe Room"],
                "applicable_meal_plans": [],
                "rule_type": "surcharge"
            },
            {
                "rule_name": "Extra Adult Charge",
                "start_date": None,
                "end_date": None,
                "charge_type": "per_adult",
                "amount": 1500,
                "applicable_room_categories": [],
                "applicable_meal_plans": [],
                "rule_type": "extra_person"
            }
        ]
    }
    
    # Test different booking scenarios
    booking_scenarios = [
        {
            "name": "Regular Booking",
            "check_in": "2025-07-25",
            "room_category": "Deluxe Room",
            "meal_plan": "map",
            "adults": 2,
            "children": 0
        },
        {
            "name": "Weekend Booking with Surcharge",
            "check_in": "2025-07-26",
            "room_category": "Deluxe Room",
            "meal_plan": "ap",
            "adults": 3,
            "children": 1
        },
        {
            "name": "Peak Season Booking",
            "check_in": "2025-12-25",
            "room_category": "Suite Room",
            "meal_plan": "ap",
            "adults": 2,
            "children": 0
        }
    ]
    
    # Import the pricing function locally
    import sys
    import os
    sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))
    from extract_tariff import calculate_final_price
    
    for scenario in booking_scenarios:
        print(f"\n📋 Testing: {scenario['name']}")
        print("-" * 40)
        
        result = calculate_final_price(
            sample_extracted_data,
            scenario['check_in'],
            scenario['room_category'],
            scenario['meal_plan'],
            scenario['adults'],
            scenario['children']
        )
        
        if result:
            print_api_response_format(result, scenario)
        else:
            print("❌ Pricing calculation failed")

def print_api_response_format(pricing_result, scenario):
    """Print the pricing result in API response format."""
    
    # Format for API response
    api_response = {
        "success": True,
        "booking_details": {
            "check_in_date": scenario['check_in'],
            "room_category": scenario['room_category'],
            "meal_plan": scenario['meal_plan'].upper(),
            "guests": {
                "adults": scenario['adults'],
                "children": scenario['children']
            }
        },
        "pricing": {
            "base_price": pricing_result['base_price'],
            "surcharges": pricing_result['surcharges'],
            "discounts": pricing_result['discounts'],
            "extra_person_charges": pricing_result['extra_person_charges'],
            "mandatory_fees": pricing_result['mandatory_fees'],
            "final_price": pricing_result['final_price'],
            "currency": "INR"
        },
        "applied_rules": [
            {
                "name": rule['rule_name'],
                "type": rule['rule_type'],
                "amount": rule['amount'],
                "description": rule['description']
            }
            for rule in pricing_result['applied_rules']
        ],
        "breakdown_summary": {
            "total_base": pricing_result['base_price'],
            "total_additions": (
                pricing_result['surcharges'] + 
                pricing_result['extra_person_charges'] + 
                pricing_result['mandatory_fees']
            ),
            "total_deductions": pricing_result['discounts'],
            "final_amount": pricing_result['final_price']
        }
    }
    
    print("✅ API Response Format:")
    print(json.dumps(api_response, indent=2, ensure_ascii=False))

def create_flask_endpoint_example():
    """Show how to integrate this into a Flask endpoint."""
    
    flask_code = '''
@app.route('/api/calculate-price', methods=['POST'])
def calculate_room_price():
    """
    Enhanced pricing calculation endpoint.
    
    Expected JSON payload:
    {
        "tariff_data": {...},  # From PDF extraction
        "check_in_date": "2025-07-26",
        "room_category": "Deluxe Room",
        "meal_plan": "map",
        "adults": 2,
        "children": 0
    }
    """
    try:
        data = request.get_json()
        
        # Validate required fields
        required_fields = ['tariff_data', 'check_in_date', 'room_category', 'meal_plan']
        for field in required_fields:
            if field not in data:
                return jsonify({
                    'success': False,
                    'error': f'Missing required field: {field}'
                }), 400
        
        # Extract parameters
        tariff_data = data['tariff_data']
        check_in_date = data['check_in_date']
        room_category = data['room_category']
        meal_plan = data['meal_plan']
        adults = data.get('adults', 2)
        children = data.get('children', 0)
        
        # Calculate pricing using enhanced engine
        pricing_result = calculate_final_price(
            tariff_data,
            check_in_date,
            room_category,
            meal_plan,
            adults,
            children
        )
        
        if not pricing_result:
            return jsonify({
                'success': False,
                'error': 'Could not calculate pricing for the given parameters'
            }), 400
        
        # Format response
        response = {
            'success': True,
            'booking_details': {
                'check_in_date': check_in_date,
                'room_category': room_category,
                'meal_plan': meal_plan.upper(),
                'guests': {'adults': adults, 'children': children}
            },
            'pricing': {
                'base_price': pricing_result['base_price'],
                'surcharges': pricing_result['surcharges'],
                'discounts': pricing_result['discounts'],
                'extra_person_charges': pricing_result['extra_person_charges'],
                'mandatory_fees': pricing_result['mandatory_fees'],
                'final_price': pricing_result['final_price'],
                'currency': 'INR'
            },
            'applied_rules': [
                {
                    'name': rule['rule_name'],
                    'type': rule['rule_type'],
                    'amount': rule['amount'],
                    'description': rule['description']
                }
                for rule in pricing_result['applied_rules']
            ]
        }
        
        return jsonify(response)
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': f'Pricing calculation failed: {str(e)}'
        }), 500
'''
    
    print(f"\n\n🔧 Flask Endpoint Integration Example")
    print("=" * 60)
    print("Here's how to add the enhanced pricing to your Flask app:")
    print(flask_code)

def main():
    """Main function to run the API integration test."""
    try:
        test_pricing_api_integration()
        create_flask_endpoint_example()
        
        print(f"\n\n✅ API Integration Test Completed!")
        print("💡 Integration Benefits:")
        print("   • Detailed pricing breakdowns for customers")
        print("   • Room category-specific rule application")
        print("   • Date-sensitive pricing calculations")
        print("   • Flexible rule types (surcharges, discounts, fees)")
        print("   • Support for extra person charges")
        print("   • Comprehensive API response format")
        
    except Exception as e:
        print(f"❌ API integration test failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
