#!/usr/bin/env python3
"""
Health check script for the Flask application
Tests if all dependencies can be imported and the app can start
"""

import sys
import os

def test_imports():
    """Test if all required modules can be imported"""
    print("🧪 Testing imports...")
    
    try:
        import flask
        print("✅ Flask imported successfully")
    except ImportError as e:
        print(f"❌ Flask import failed: {e}")
        return False
    
    try:
        import flask_cors
        print("✅ Flask-CORS imported successfully")
    except ImportError as e:
        print(f"❌ Flask-CORS import failed: {e}")
        return False
    
    try:
        import requests
        print("✅ Requests imported successfully")
    except ImportError as e:
        print(f"❌ Requests import failed: {e}")
        return False
    
    try:
        from dotenv import load_dotenv
        print("✅ Python-dotenv imported successfully")
    except ImportError as e:
        print(f"❌ Python-dotenv import failed: {e}")
        return False
    
    try:
        import langchain_google_genai
        print("✅ LangChain Google GenAI imported successfully")
    except ImportError as e:
        print(f"❌ LangChain Google GenAI import failed: {e}")
        return False
    
    try:
        import pydantic
        print("✅ Pydantic imported successfully")
    except ImportError as e:
        print(f"❌ Pydantic import failed: {e}")
        return False
    
    return True

def test_app_creation():
    """Test if the Flask app can be created"""
    print("\n🏗️ Testing Flask app creation...")
    
    try:
        # Add current directory to path
        sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))
        
        from app import app
        print("✅ Flask app created successfully")
        
        # Test if routes are registered
        routes = [rule.rule for rule in app.url_map.iter_rules()]
        print(f"✅ Found {len(routes)} routes: {routes}")
        
        return True
        
    except Exception as e:
        print(f"❌ Flask app creation failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_extract_tariff_import():
    """Test if extract_tariff module can be imported"""
    print("\n📄 Testing extract_tariff module...")
    
    try:
        from extract_tariff import orchestrate_tariff_extraction
        print("✅ extract_tariff module imported successfully")
        return True
    except Exception as e:
        print(f"❌ extract_tariff import failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def check_environment():
    """Check environment variables"""
    print("\n🌍 Checking environment...")
    
    # Load environment variables
    try:
        from dotenv import load_dotenv
        load_dotenv()
        print("✅ Environment file loaded")
    except:
        print("⚠️ No .env file found or failed to load")
    
    # Check for required environment variables
    google_api_key = os.getenv('GOOGLE_API_KEY')
    if google_api_key and google_api_key != 'your_google_api_key_here':
        print("✅ GOOGLE_API_KEY is configured")
    else:
        print("⚠️ GOOGLE_API_KEY is not configured or using placeholder")
    
    mistral_api_key = os.getenv('MISTRAL_API_KEY')
    if mistral_api_key and mistral_api_key != 'your_mistral_api_key_here':
        print("✅ MISTRAL_API_KEY is configured")
    else:
        print("⚠️ MISTRAL_API_KEY is not configured or using placeholder")

def main():
    """Main health check function"""
    print("🏥 Backend PDF Extraction - Health Check")
    print("=" * 50)
    
    all_tests_passed = True
    
    # Test imports
    if not test_imports():
        all_tests_passed = False
    
    # Test extract_tariff module
    if not test_extract_tariff_import():
        all_tests_passed = False
    
    # Test Flask app creation
    if not test_app_creation():
        all_tests_passed = False
    
    # Check environment
    check_environment()
    
    print("\n" + "=" * 50)
    if all_tests_passed:
        print("🎉 All health checks passed! The application should run successfully.")
        sys.exit(0)
    else:
        print("❌ Some health checks failed. Please fix the issues before running the application.")
        sys.exit(1)

if __name__ == "__main__":
    main()
